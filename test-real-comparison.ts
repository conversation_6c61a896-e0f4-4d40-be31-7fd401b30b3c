import { EventAdapter } from './src/parsers/eventAdapter';
import { Dispatcher } from './src/dispatcher';
import { GrpcClient } from './src/geyserClient';
import { METEORA_PROGRAM_IDS } from './src/parsers/meteora/constants';
import { SolanaDexTrade, SolanaDexNewPool } from './src/types';
import util from 'util';
import fs from 'fs';
import bs58 from 'bs58';
/**
 * 简化事件解析器测试
 * 使用真实的Yellowstone gRPC数据流测试事件解析功能
 */
async function testEventParser() {
    console.log('🧪 === 简化事件解析器测试 ===');
    console.log('📡 使用真实Yellowstone gRPC数据流...\n');

    // 检查环境变量
    if (!process.env.GEYSER_GRPC_ADDRESS) {
        console.error('❌ 请设置环境变量 GEYSER_GRPC_ADDRESS');
        console.log('例如: export GEYSER_GRPC_ADDRESS="your-geyser-endpoint:port"');
        process.exit(1);
    }

    // 创建 Dispatcher 和简化事件解析器
    const dispatcher = new Dispatcher();
    const eventAdapter = new EventAdapter();

    // 注册解析器
    METEORA_PROGRAM_IDS.forEach(programId => {
        dispatcher.register(programId, eventAdapter);
    });

    console.log(`📋 已注册 ${METEORA_PROGRAM_IDS.length} 个 Meteora 程序`);
    console.log('🔧 使用简化事件解析器 - 直接从交易日志解析事件\n');

    // 统计变量
    let totalTransactions = 0;
    let validTransactions = 0;
    let totalTrades = 0;
    let totalNewPools = 0;
    let successfulParses = 0;
    let processingTimes: number[] = [];

    const startTime = Date.now();

    // 设置事件监听
    dispatcher.on('trade', (trades: SolanaDexTrade[]) => {
        totalTrades += trades.length;
        successfulParses++;
        
        console.log(`\n✅ [事件解析器] 解析出 ${trades.length} 个交易事件`);
        
        trades.forEach((trade, idx) => {
            console.log(`📊 交易 ${idx + 1}:`);
            console.log(`  🏪 DEX: ${trade.dexName}`);
            console.log(`  🔄 类型: ${trade.txType}`);
            console.log(`  📍 来源: ${trade.dexSource}`);
            console.log(`  🪙 代币交换: ${trade.token0Amount} → ${trade.token1Amount}`);
            console.log(`  👤 交易者: ${trade.traderAddress.substring(0, 16)}...`);
            console.log(`  🏊 池地址: ${trade.poolAddress.substring(0, 16)}...`);
            console.log(`  #️⃣ 交易哈希: ${trade.txHash.substring(0, 16)}...`);
            console.log(`  🕒 时间: ${new Date(trade.timestamp * 1000).toLocaleString()}`);
        });
    });

    dispatcher.on('new_pool', (newPools: SolanaDexNewPool[]) => {
        totalNewPools += newPools.length;
        successfulParses++;
        
        console.log(`\n🆕 [事件解析器] 解析出 ${newPools.length} 个新池事件`);
        
        newPools.forEach((pool, idx) => {
            console.log(`🏊 新池 ${idx + 1}:`);
            console.log(`  🏪 DEX: ${pool.dexName}`);
            console.log(`  📍 池地址: ${pool.pairAddress.substring(0, 16)}...`);
            console.log(`  🪙 代币对: ${pool.token0Address.substring(0, 8)}... / ${pool.token1Address.substring(0, 8)}...`);
            console.log(`  #️⃣ 创建交易: ${pool.creationTxHash.substring(0, 16)}...`);
            console.log(`  🏗️ 区块号: ${pool.blockNumber}`);
            console.log(`  🕒 时间: ${new Date(pool.timestamp * 1000).toLocaleString()}`);
        });
    });

    // 连接到真实的 Yellowstone gRPC 数据流
    const grpcClient = GrpcClient.getInstance();
    
    console.log(`📡 开始订阅真实的 Yellowstone 数据流...`);
    console.log(`🎯 监听程序: ${METEORA_PROGRAM_IDS.length} 个 Meteora 程序`);
    console.log('');

    grpcClient.subscribe(METEORA_PROGRAM_IDS, async (transactionUpdate) => {
        totalTransactions++;
        const currentTime = new Date().toLocaleTimeString();
        
        console.log(`\n📥 [${currentTime}] 数据包 #${totalTransactions}`);
        
        if (!transactionUpdate?.transaction?.transaction) {
            console.log('❌ 无效数据包，跳过');
            return;
        }

        validTransactions++;
        const tx = transactionUpdate.transaction;
        console.log(tx.transaction.signature);
        // 修正数据结构访问路径
        console.log('📋 交易信息:', {
            插槽: tx.slot,
            签名: tx.transaction.signature?
            bs58.encode(Buffer.from(tx.transaction.signature)): 'N/A',
            账户数: tx.transaction.transaction.message?.accountKeys?.length || 0,
            指令数: tx.transaction.transaction.message?.instructions?.length || 0,
            日志数: tx.transaction.meta?.logMessages?.length || 0,
            状态: tx.meta?.err ? '失败' : '成功'
        });

        // 检查事件日志 - 修正数据结构访问路径
        const eventLogs = tx.meta?.logMessages?.filter((log: string) => 
            log.startsWith('Program data:')
        ) || [];
        
        if (eventLogs.length > 0) {
            console.log(`🎯 发现 ${eventLogs.length} 个事件日志`);
        }

        try {
            const startTime = Date.now();
            
            // 注意：不需要转换数据格式，直接使用transactionUpdate
            dispatcher.dispatch(transactionUpdate);
            
            const processingTime = Date.now() - startTime;
            processingTimes.push(processingTime);
            successfulParses++;
            
            console.log(`✅ 处理完成 (${processingTime}ms)`);
            
        } catch (error) {
            console.error('🚨 处理失败:', error);
        }
        
        // 定期显示统计信息
        if (totalTransactions % 10 === 0) {
            const runTime = (Date.now() - startTime) / 1000;
            const avgProcessingTime = processingTimes.length > 0 ? 
                processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length : 0;
            
            console.log(`\n📊 [统计] 运行时间: ${runTime.toFixed(1)}s`);
            console.log(`  - 总数据包: ${totalTransactions}`);
            console.log(`  - 有效交易: ${validTransactions}`);
            console.log(`  - 成功处理: ${successfulParses}`);
            console.log(`  - 平均处理时间: ${avgProcessingTime.toFixed(2)}ms`);
            console.log(`  - 事件总数: ${totalTrades + totalNewPools}`);
        }
        
        console.log('─'.repeat(60));
    });

    // 定期统计报告
    const statsInterval = setInterval(() => {
        const runtime = Math.floor((Date.now() - startTime) / 1000);
        
        console.log(`\n📈 === 事件解析统计报告 (运行 ${runtime}s) ===`);
        
        console.log(`📊 数据流统计:`);
        console.log(`  📥 总接收交易: ${totalTransactions}`);
        console.log(`  ✅ 有效交易: ${validTransactions}`);
        console.log(`  📈 有效率: ${totalTransactions > 0 ? ((validTransactions / totalTransactions) * 100).toFixed(2) : '0.00'}%`);
        
        console.log(`\n🎯 事件解析结果:`);
        console.log(`  📊 交易事件总数: ${totalTrades}`);
        console.log(`  🆕 新池事件总数: ${totalNewPools}`);
        console.log(`  ✅ 成功解析次数: ${successfulParses}`);
        console.log(`  📈 解析成功率: ${validTransactions > 0 ? ((successfulParses / validTransactions) * 100).toFixed(2) : '0.00'}%`);
        
        if (processingTimes.length > 0) {
            const avgTime = processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length;
            console.log(`  ⏱️ 平均处理时间: ${avgTime.toFixed(2)}ms`);
        }
        
        console.log(`\n🚀 处理效率:`);
        if (runtime > 0) {
            console.log(`  📈 平均TPS: ${(totalTransactions / runtime).toFixed(2)} 交易/秒`);
            console.log(`  📊 事件提取率: ${(totalTrades + totalNewPools)} 个事件 / ${runtime}s`);
        }
        
        // 适配器状态
        const adapterStatus = eventAdapter.getStatus();
        console.log(`\n🔧 解析器状态:`);
        console.log(`  📊 类型: ${adapterStatus.type}`);
        console.log(`  ✅ 状态: ${adapterStatus.ready ? '就绪' : '未就绪'}`);
        console.log(`  🎯 支持程序: ${adapterStatus.supportedPrograms} 个`);
        
        console.log('');
    }, 20000); // 每20秒报告一次

    // 优雅关闭
    process.on('SIGINT', async () => {
        console.log('\n🔄 正在停止事件解析测试...');
        clearInterval(statsInterval);
        
        const endTime = Date.now();
        const totalRuntime = Math.floor((endTime - startTime) / 1000);
        
        console.log('\n📊 === 事件解析测试最终报告 ===');
        console.log(`⏱️ 总运行时间: ${totalRuntime}秒`);
        console.log(`📡 处理交易总数: ${totalTransactions}`);
        console.log(`✅ 有效交易数: ${validTransactions}`);
        
        console.log(`\n🎯 事件解析最终统计:`);
        console.log(`  📊 交易事件总数: ${totalTrades}`);
        console.log(`  🆕 新池事件总数: ${totalNewPools}`);
        console.log(`  ✅ 成功解析次数: ${successfulParses}`);
        console.log(`  📈 解析成功率: ${validTransactions > 0 ? ((successfulParses / validTransactions) * 100).toFixed(2) : '0.00'}%`);
        
        if (processingTimes.length > 0) {
            const avgTime = processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length;
            const minTime = Math.min(...processingTimes);
            const maxTime = Math.max(...processingTimes);
            
            console.log(`\n⏱️ 性能统计:`);
            console.log(`  📊 平均处理时间: ${avgTime.toFixed(2)}ms`);
            console.log(`  ⚡ 最快处理时间: ${minTime}ms`);
            console.log(`  🐌 最慢处理时间: ${maxTime}ms`);
        }
        
        console.log(`\n🚀 处理效率:`);
        if (totalRuntime > 0) {
            console.log(`  📈 平均TPS: ${(totalTransactions / totalRuntime).toFixed(2)} 交易/秒`);
            console.log(`  📊 事件提取效率: ${((totalTrades + totalNewPools) / totalRuntime).toFixed(2)} 事件/秒`);
        }
        
        console.log(`\n🎯 事件解析器优势:`);
        console.log(`  ✅ 直接解析交易日志中的事件数据`);
        console.log(`  ✅ 使用 Anchor 的 coder.events.decode()`);
        console.log(`  ✅ 代码简洁，性能优异`);
        console.log(`  ✅ 符合 Anchor 程序设计理念`);
        
        console.log(`\n✅ 事件解析测试完成`);
        process.exit(0);
    });

    console.log('🚀 事件解析测试已启动！');
    console.log('💡 使用真实的 Yellowstone gRPC 数据流');
    console.log('🎯 专注于事件解析功能验证');
    console.log('📊 实时显示解析出的交易和新池事件');
    console.log('⏹️ Ctrl+C 停止测试并查看最终报告');
    console.log('============================================================\n');
}

// 运行事件解析测试
if (require.main === module) {
    testEventParser().catch(error => {
        console.error('💥 事件解析测试启动失败:', error);
        process.exit(1);
    });
}


export { testEventParser }; 