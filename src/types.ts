// 来自 Geyser proto 的交易简化类型
export type ConfirmedTransaction = any;

/**
 * DEX 交易事件的标准化接口。
 */
export interface SolanaDexTrade {
    dexName: string;
    poolAddress: string;
    txHash: string;
    txType: 'buy' | 'sell' | 'add_liquidity' | 'remove_liquidity';
    slot: number;
    instructionIndex: string; // 新增，指令索引
    timestamp: number;
    traderAddress: string;
    token0Amount: string; // 使用字符串以通过 Decimal.js 保证高精度
    token1Amount: string;
    token0Address: string;
    token1Address: string;
    token0Reserve: string; // 新增，池子token0余额
    token1Reserve: string; // 新增，池子token1余额
    dexSource: string; // 新增，来源
}

/**
 * 新 DEX 池创建事件的标准化接口。
 */
export interface SolanaDexNewPool {
    dexName: string;
    pairAddress: string;
    token0Address: string;
    token1Address: string;
    creationTxHash: string;
    blockNumber: number;
    timestamp: number;
}

/**
 * 所有 DEX 解析器必须实现的接口。
 */
export interface IParser {
    /**
     * 解析原始交易并返回标准化的交易或新池子事件数组。
     * @param transaction - 来自 Geyser 数据流的已确认交易对象。
     * @returns 解析后的事件数组，如果交易不相关则返回 null。
     */
    parse(transaction: ConfirmedTransaction): (SolanaDexTrade | SolanaDexNewPool)[] | null;
}

// ==================== 通用解析器类型定义 ====================
// 这些类型可以被任何Solana DEX解析器使用

/**
 * 解析处理结果 - 适用于任何DEX解析器
 */
export interface ProcessingResult {
    success: boolean;
    processingTime: number;
    tradesCount: number;
    poolsCount: number;
    extractedData: CompleteTransactionData | null;
    standardizedData: StandardizedData | null;
    error: string | null;
}

/**
 * 标准化数据结果 - 适用于任何DEX
 */
export interface StandardizedData {
    trades: SolanaDexTrade[];
    pools: SolanaDexNewPool[];
}

/**
 * 消息队列发布结果 - 适用于任何DEX
 */
export interface PublishResult {
    tradesPublished: number;
    poolsPublished: number;
}

/**
 * 完整交易数据 - 适用于任何Solana程序
 * 包含从IDL提取的四层数据源
 */
export interface CompleteTransactionData {
    instructions: InstructionData[];
    accountStates: AccountStateData[];
    metadata: {
        txHash: string;
        slot: number;
        timestamp: number;
        aggregatorContext?: any;
    };
}


/**
 * 指令数据
 */
export interface InstructionData {
    name: string;
    programId: string;
    index: number;
    instructionId: string; // 新增：用于标识指令位置（top_0, inner_0_1等）
    data: any;
    accounts: Record<string, string>;
}


/**
 * 账户状态数据 - 适用于任何Solana程序
 */
export interface AccountStateData {
    address: string;
    data: any;      // 解码后的账户数据
    owner: string;  // 账户所有者程序ID
}


// ==================== 扩展接口 ====================

/**
 * 增强的解析器接口 - 支持现代化功能
 */
export interface IAdvancedParser extends IParser {
    /**
     * 异步处理交易，返回详细结果
     */
    processTransaction(transaction: ConfirmedTransaction): Promise<ProcessingResult>;
    
    /**
     * 初始化解析器
     */
    initialize(): Promise<void>;

}
