import Client, { CommitmentLevel, SubscribeRequest } from '@triton-one/yellowstone-grpc';
import { config } from 'dotenv';

config(); // 加载 .env 文件

// 订阅更新类型
type SubscribeUpdate = any;

/**
 * 基于 Triton Yellowstone gRPC 的现代化 Geyser 客户端
 * 提供高性能、稳定的 Solana 区块链数据流连接
 */
export class GrpcClient {
    private static instance: GrpcClient;
    private client: Client;
    private isConnected: boolean = false;

    private constructor() {
        console.log('初始化gRPC客户端');
        
        // 检查环境变量
        const serverAddress = process.env.GEYSER_GRPC_ADDRESS;
        const token = process.env.GEYSER_TOKEN;
        
        if (!serverAddress) {
            throw new Error('GEYSER_GRPC_ADDRESS 环境变量未设置');
        }
        
        console.log(`目标服务器: ${serverAddress}`);
        
        try {
            // 创建客户端配置
            const clientOptions = {
                // 设置最大接收消息大小 (64MB)
                "grpc.max_receive_message_length": 64 * 1024 * 1024,
                // 设置keepalive参数
                "grpc.keepalive_time_ms": 30000,
                "grpc.keepalive_timeout_ms": 5000,
                "grpc.keepalive_permit_without_calls": 1,
                "grpc.http2.max_pings_without_data": 0,
                "grpc.http2.min_time_between_pings_ms": 10000,
                "grpc.http2.min_ping_interval_without_data_ms": 300000,
            };

            console.log('正在创建gRPC 客户端...');
            
            // 初始化 Triton Yellowstone gRPC 客户端
            this.client = new Client(serverAddress, token,clientOptions);
            
            console.log('gRPC 客户端初始化成功！');
            console.log(`连接目标: ${serverAddress}`);
            
            this.isConnected = true;
            
        } catch (error) {
            console.error('gRPC 客户端初始化失败:', error);
            throw error;
        }
    }

    public static getInstance(): GrpcClient {
        if (!GrpcClient.instance) {
            GrpcClient.instance = new GrpcClient();
        }
        return GrpcClient.instance;
    }

    /**
     * 使用 Yellowstone gRPC 协议订阅 Solana 区块链数据
     * @param programIds - 要订阅的程序地址数组
     * @param onData - 接收到数据时的回调函数
     */
    public async subscribe(programIds: string[], onData: (data: SubscribeUpdate) => void): Promise<void> {

        if (!this.isConnected) {
            throw new Error('gRPC客户端未连接，请先初始化');
        }
        
        // 验证环境变量
        if (!process.env.GEYSER_GRPC_ADDRESS) {
            throw new Error('GEYSER_GRPC_ADDRESS 环境变量未设置');
        }
        
        console.log('要订阅的Meteora程序ID:');
        programIds.forEach((id, index) => {
            console.log(`  ${index + 1}. ${id}`);
        });
        
        let dataReceived = 0;
        let heartbeatCount = 0;
        let connectionEstablished = false;
        let subscriptionSent = false;

        try {
            console.log('正在建立gRPC 数据流连接...');

            // 创建订阅流
            console.log('创建订阅流...');
            const stream = await this.client.subscribe();
            console.log('订阅流创建成功');

            // 设置数据处理器
            stream.on('data', (data: SubscribeUpdate) => {
                if (!connectionEstablished) {
                    connectionEstablished = true;
                    console.log('gRPC 数据流连接已建立');
                }
                
                dataReceived++;
                const timestamp = new Date().toLocaleTimeString();
                
                // 调用用户回调
                try {
                    onData(data);
                } catch (callbackError) {
                    console.error('回调函数执行失败:', callbackError);
                }
            });

            stream.on('error', (err: any) => {
                console.error('gRPC 数据流错误:', err);
            });

            stream.on('end', () => {
                console.log(' gRPC 数据流已结束');
            });

            // 构建订阅请求 - 使用正确的Yellowstone gRPC格式
            const subscribeRequest: SubscribeRequest = {
                transactions: {
                    meteora_txs: {
                        accountInclude: programIds,
                        accountExclude: [],
                        accountRequired: [],
                        vote: false,          
                        failed: false        
                    }
                },
                accounts: {},
                slots: {},
                transactionsStatus: {},
                blocks: {},
                blocksMeta: {},
                entry: {},
                accountsDataSlice: [],
                commitment: CommitmentLevel.PROCESSED
            };

            console.log('订阅请求详情:');
            console.log('目标程序列表:');
            programIds.forEach((id, index) => {
                console.log(`  ${index + 1}. ${id}`);
            });

            // 发送订阅请求
            stream.write(subscribeRequest);


            // 设置定期ping以保持连接
            const pingInterval = setInterval(() => {
                try {
                    const pingRequest: SubscribeRequest = {
                        ping: { id: Date.now() },
                        accounts: {},
                        transactions: {},
                        slots: {},
                        transactionsStatus: {},
                        blocks: {},
                        blocksMeta: {},
                        entry: {},
                        accountsDataSlice: []
                    };
                    stream.write(pingRequest);
                } catch (error) {
                    console.error('发送心跳失败:', error);
                    clearInterval(pingInterval);
                }
            }, 5000); // 每5秒发送一次心跳

            // 优雅关闭处理
            process.on('SIGINT', () => {
                console.log('\n🔄 正在关闭 gRPC 连接...');
                clearInterval(pingInterval);
                stream.end();
                process.exit(0);
            });

        } catch (error) {
            console.error('建立 gRPC 订阅失败:', error);
            console.error(`  5. 当前设置: ${process.env.GEYSER_GRPC_ADDRESS}`);
            throw error;
        }
    }
}