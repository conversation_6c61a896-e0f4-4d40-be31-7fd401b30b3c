/**
 * Usage examples for the IsChainNativeOrStableCoin function
 * Demonstrates how to use the TypeScript implementation
 */

import {
    ChainName,
    IsChainNativeOrStableCoin,
    is<PERSON>hainNativeCoin,
    is<PERSON>hainStableCoin,
    parse<PERSON>hainName,
    getBaseTokenAddress,
    getChainNativeCoinInfo,
    getChainStableCoinInfo
} from '../constants';

console.log('🚀 IsChainNativeOrStableCoin Usage Examples\n');

// Example 1: Basic usage with enum
console.log('📋 Example 1: Basic usage with ChainName enum');
const solanaUSDC = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
const solanaSOL = 'So11111111111111111111111111111111111111112';
const randomToken = 'SomeRandomTokenAddress123456789';

console.log(`IsChainNativeOrStableCoin(Solana, USDC): ${IsChainNativeOrStableCoin(ChainName.Solana, solanaUSDC)}`);
console.log(`IsChainNativeOrStableCoin(Solana, SOL): ${IsChainNativeOrStableCoin(ChainName.Solana, solanaSOL)}`);
console.log(`IsChainNativeOrStableCoin(Solana, Random): ${IsChainNativeOrStableCoin(ChainName.Solana, randomToken)}`);

// Example 2: Using with string chain names (like in dataHandler)
console.log('\n📋 Example 2: Using with string chain names');
function checkTokenWithStringChain(chainName: string, tokenAddress: string): boolean {
    const parsedChain = parseChainName(chainName);
    if (!parsedChain) {
        console.log(`❌ Invalid chain name: ${chainName}`);
        return false;
    }
    return IsChainNativeOrStableCoin(parsedChain, tokenAddress);
}

console.log(`checkTokenWithStringChain('solana', USDC): ${checkTokenWithStringChain('solana', solanaUSDC)}`);
console.log(`checkTokenWithStringChain('SOLANA', SOL): ${checkTokenWithStringChain('SOLANA', solanaSOL)}`);
console.log(`checkTokenWithStringChain('bsc', BNB USDT): ${checkTokenWithStringChain('bsc', '0x55d398326f99059ff775485246999027b3197955')}`);

// Example 3: Separate checks for native vs stable coins
console.log('\n📋 Example 3: Separate native vs stable coin checks');
const tokens = [
    { chain: ChainName.Solana, address: solanaSOL, name: 'SOL' },
    { chain: ChainName.Solana, address: solanaUSDC, name: 'USDC' },
    { chain: ChainName.BnbChain, address: '0xbb4cdb9cbd36b01bd1cbaebf2de08d9173bc095c', name: 'WBNB' },
    { chain: ChainName.BnbChain, address: '0x55d398326f99059ff775485246999027b3197955', name: 'USDT' }
];

tokens.forEach(token => {
    const isNative = isChainNativeCoin(token.chain, token.address);
    const isStable = isChainStableCoin(token.chain, token.address);
    const isNativeOrStable = IsChainNativeOrStableCoin(token.chain, token.address);
    
    console.log(`${token.name} (${token.chain}):`);
    console.log(`  Native: ${isNative}, Stable: ${isStable}, Native or Stable: ${isNativeOrStable}`);
});

// Example 4: Getting base token address
console.log('\n📋 Example 4: Getting base token address');
const baseTokenExamples = [
    {
        chain: ChainName.Solana,
        token0: solanaSOL,
        token1: 'SomeOtherToken',
        description: 'SOL vs Other Token'
    },
    {
        chain: ChainName.Solana,
        token0: 'SomeToken',
        token1: solanaUSDC,
        description: 'Some Token vs USDC'
    },
    {
        chain: ChainName.BnbChain,
        token0: '0xbb4cdb9cbd36b01bd1cbaebf2de08d9173bc095c',
        token1: 'SomeOtherToken',
        description: 'WBNB vs Other Token'
    }
];

baseTokenExamples.forEach(example => {
    const baseToken = getBaseTokenAddress(example.chain, example.token0, example.token1);
    console.log(`${example.description}: Base token is ${baseToken}`);
});

// Example 5: Getting coin information
console.log('\n📋 Example 5: Getting coin information');
const nativeCoinInfo = getChainNativeCoinInfo(ChainName.Solana);
console.log('Solana native coin info:', nativeCoinInfo);

const stableCoinInfo = getChainStableCoinInfo(ChainName.Solana, solanaUSDC);
console.log('Solana USDC info:', stableCoinInfo);

// Example 6: Error handling
console.log('\n📋 Example 6: Error handling');
try {
    const invalidChain = parseChainName('invalid-chain');
    console.log(`Invalid chain result: ${invalidChain}`);
    
    // This would be safe because parseChainName returns null for invalid chains
    if (invalidChain) {
        const result = IsChainNativeOrStableCoin(invalidChain, solanaUSDC);
        console.log(`Result: ${result}`);
    } else {
        console.log('✅ Properly handled invalid chain name');
    }
} catch (error) {
    console.log(`❌ Error: ${error}`);
}

// Example 7: Case sensitivity demonstration
console.log('\n📋 Example 7: Case sensitivity demonstration');
const caseSensitivityTests = [
    {
        chain: ChainName.Solana,
        address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // correct case
        description: 'Solana USDC (correct case)'
    },
    {
        chain: ChainName.Solana,
        address: 'epjfwdd5aufqssqem2qn1xzybapC8G4wEGGkZwyTDt1v', // wrong case
        description: 'Solana USDC (wrong case)'
    },
    {
        chain: ChainName.BnbChain,
        address: '0x55d398326f99059ff775485246999027b3197955', // lowercase
        description: 'BSC USDT (lowercase)'
    },
    {
        chain: ChainName.BnbChain,
        address: '0x55D398326f99059fF775485246999027B3197955', // mixed case
        description: 'BSC USDT (mixed case)'
    }
];

caseSensitivityTests.forEach(test => {
    const result = IsChainNativeOrStableCoin(test.chain, test.address);
    console.log(`${test.description}: ${result ? '✅' : '❌'} ${result}`);
});

console.log('\n🎉 Examples completed!');
