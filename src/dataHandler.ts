import { SolanaDexNewPool, SolanaDexTrade } from "./types";
import { MessageBroker, KAFKA_TOPICS, WS_MESSAGE_TOPICS } from "./services/messageBroker";

// 业务相关类型补充
interface EnrichedTrade extends SolanaDexTrade {
    usdPrice?: string;
    totalValueUSD?: string;
    isLarge?: boolean;
    // ...其他 enrich 字段
}

// 流动性事件类型（对应 Go 的 helius.Liquidity）
interface SolanaLiquidity {
    pairAddress: string;
    lpMintAddress?: string;
    userOwnerAddress: string;
    token0Address: string;
    token1Address: string;
    token0Amount: string;
    token1Amount: string;
    liquidityType: 'add' | 'remove';
    txHash: string;
    slot: number;
    txTimestamp: number;
    txInstructionIndex: string;
    dexName: string;
    createTime: number;
}

// WebSocket 推送消息结构（对应 Go 的 websocket.SocketMessageMqData）
interface WebSocketMessage {
    topicName: string;
    socketMessageData: {
        uuid: string;
        timestamp: number;
        messageType: string;
        data: any;
    };
    isAllUser?: boolean;
    userId?: number;
    connId?: string;
}

/**
 * 在数据被解析和标准化后处理数据。
 */
export class DataHandler {
    private readonly broker: MessageBroker;

    constructor() {
        this.broker = MessageBroker.getInstance();
    }

    // 过滤无效交易（对应 Go 端的跳过0的swap逻辑）
    private isValidTrade(trade: SolanaDexTrade): boolean {
        return trade.token0Amount !== "0" && trade.token1Amount !== "0" && !!trade.txType;
    }

    // 判断是否为异常交易（对应 Go 端的 isUnusualTx）
    private isUnusualTx(transaction: any): boolean {
        // TODO: 实现异常交易检测逻辑
        return false;
    }

    // 判断是否为原生币或稳定币（对应 Go 端的 consts.IsChainNativeOrStableCoin）
    private isChainNativeOrStableCoin(chainName: string, tokenAddress: string): boolean {
        const { IsChainNativeOrStableCoin, parseChainName } = require('./constants');
        const parsedChainName = parseChainName(chainName);
        if (!parsedChainName) {
            return false;
        }
        return IsChainNativeOrStableCoin(parsedChainName, tokenAddress);
    }

    // 获取基础代币地址（对应 Go 端的 consts.GetBaseTokenAddress）
    private getBaseTokenAddress(chainName: string, token0Address: string, token1Address: string): string {
        const { getBaseTokenAddress, parseChainName } = require('./constants');
        const parsedChainName = parseChainName(chainName);
        if (!parsedChainName) {
            return token0Address; // Default fallback
        }
        return getBaseTokenAddress(parsedChainName, token0Address, token1Address);
    }

    // 获取代币地址（对应 Go 端的 TokenService.GetTokenAddress）
    private getTokenAddress(chainName: string, token0Address: string, token1Address: string): string {
        // 返回非原生代币地址
        if (token0Address !== this.SOLANA_NATIVE) {
            return token0Address;
        }
        if (token1Address !== this.SOLANA_NATIVE) {
            return token1Address;
        }
        // 如果都是原生代币，返回 token1
        return token1Address;
    }

    // enrich 业务数据（如USD估值、是否大额等）
    private enrichTrade(trade: SolanaDexTrade): EnrichedTrade {
        // TODO: 对接价格服务，计算usdPrice、totalValueUSD等
        // 这里只做结构预留，实际应该调用价格服务

        // 简化的价格计算逻辑
        let usdPrice = "0";
        let totalValueUSD = "0";

        try {
            // 如果交易涉及稳定币，可以简单估算USD价值
            const token0Amount = parseFloat(trade.token0Amount);
            const token1Amount = parseFloat(trade.token1Amount);

            if (this.isChainNativeOrStableCoin('solana', trade.token0Address)) {
                // token0是稳定币，直接使用其数量作为USD价值
                totalValueUSD = token0Amount.toString();
                if (token1Amount > 0) {
                    usdPrice = (token0Amount / token1Amount).toString();
                }
            } else if (this.isChainNativeOrStableCoin('solana', trade.token1Address)) {
                // token1是稳定币
                totalValueUSD = token1Amount.toString();
                if (token0Amount > 0) {
                    usdPrice = (token1Amount / token0Amount).toString();
                }
            }
        } catch (error) {
            console.warn('计算USD价格失败:', error);
        }

        return {
            ...trade,
            usdPrice,
            totalValueUSD,
            isLarge: parseFloat(totalValueUSD) > 10000,
        };
    }

    // 处理流动性操作（对应 Go 的 DataWarehouseService.PushSolanaLiquidity）
    private async handleLiquidity(trade: EnrichedTrade) {
        if (trade.txType === 'add_liquidity' || trade.txType === 'remove_liquidity') {
            const liquidity: SolanaLiquidity = {
                pairAddress: trade.poolAddress,
                lpMintAddress: '', // 可从交易解析获取
                userOwnerAddress: trade.traderAddress,
                token0Address: trade.token0Address,
                token1Address: trade.token1Address,
                token0Amount: trade.token0Amount,
                token1Amount: trade.token1Amount,
                liquidityType: trade.txType === 'add_liquidity' ? 'add' : 'remove',
                txHash: trade.txHash,
                slot: trade.slot,
                txTimestamp: trade.timestamp,
                txInstructionIndex: trade.instructionIndex,
                dexName: trade.dexName,
                createTime: Math.floor(Date.now() / 1000),
            };
            
            // 投递到 Kafka（对应 Go 的 solana_dex_liquidity topic）
            await this.broker.publish(KAFKA_TOPICS.SOLANA_DEX_LIQUIDITY, liquidity);
            console.log(`[Liquidity] 投递成功: ${trade.txHash}`);
        }
    }

    // 实时推送（对应 Go 的 WebSocketService.PublishMessage）
    private async pushRealTimeMessage(trade: EnrichedTrade, transaction: any) {
        const uuid = this.generateUUID();
        const timestamp = Date.now() * 1000000; // 纳秒时间戳

        // 1. 实时交易推送（对应 Go 的 SocketMessageTopicRealTimePairTrade）
        const realTimeTopicName = MessageBroker.formatTopic(WS_MESSAGE_TOPICS.REAL_TIME_PAIR_TRADE, 'Solana', transaction.poolAddress);
        const token0Amount = parseFloat(transaction.token0Amount);
        const token1Amount = parseFloat(transaction.token1Amount);
        const token0UnitPrice = parseFloat(transaction.token0UnitPrice);
        const totalValueUSD = parseFloat(transaction.totalValueUSD);
        const usdPrice = parseFloat(transaction.usdPrice);
        
        const realTimeMsg: WebSocketMessage = {
            topicName: realTimeTopicName,
            socketMessageData: {
                uuid,
                timestamp,
                messageType: realTimeTopicName,
                data: {
                    traderAddress: transaction.traderAddress,
                    txTime: transaction.timestamp,
                    txType: transaction.txType,
                    txHash: transaction.txHash,
                    price: token0UnitPrice,
                    usdPrice: usdPrice,
                    usd: totalValueUSD,
                    token0Amount: token0Amount,
                    token1Amount: token1Amount,
                    extendedInfo: {
                        isLarge: totalValueUSD > 10000,
                        dexSource: transaction.dexSource,
                    },
                },
            },
        };

        // 2. Pump 交易推送（对应 Go 的 SocketMessageTopicPumpPairTrade）
        const pumpTopicName = MessageBroker.formatTopic(WS_MESSAGE_TOPICS.PUMP_PAIR_TRADE, transaction.poolAddress);
        const pumpMsg: WebSocketMessage = {
            topicName: pumpTopicName,
            socketMessageData: {
                uuid,
                timestamp,
                messageType: pumpTopicName,
                data: {
                    address: transaction.traderAddress,
                    txTime: transaction.timestamp,
                    txType: transaction.txType,
                    txHash: transaction.txHash,
                    price: token0UnitPrice,
                    usdPrice: usdPrice,
                    usd: totalValueUSD,
                    token0Amount: token0Amount,
                    token1Amount: token1Amount,
                    timestamp,
                    extendedInfo: {
                        isFirstBuy: false, // 先不做, 影响性能
                        isTopHolder: false, // 先不做, 影响性能
                        isLargeTransaction: totalValueUSD > 10000,
                        isDev: false, // TODO LRU 缓存
                        isBotWallet: false, // 先不做, 影响性能
                        dexSource: transaction.dexSource,
                        kolLabel: {},
                    },
                },
            },
        };

        // 投递到 RabbitMQ（对应 Go 的 PushExchange）
        await this.broker.publishWebSocket(realTimeMsg);
        await this.broker.publishWebSocket(pumpMsg);
        
        console.log(`[WebSocket] 推送成功: ${trade.txHash}`);
    }

    // 生成 UUID
    private generateUUID(): string {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    public async handleTrades(trades: SolanaDexTrade[]) {
        // 1. 过滤无效/异常数据
        const validTrades = trades.filter(this.isValidTrade);
        if (validTrades.length === 0) return;

        // 2. enrich 业务数据
        const enrichedTrades = validTrades.map(this.enrichTrade);

        // 3. 处理每个交易
        for (const trade of enrichedTrades) {
            // 3.1 重新确定交易类型（对应Go端的txType判断逻辑）
            const correctedTrade = this.correctTradeType(trade);

            // 3.2 区分流动性操作与普通swap
            if (correctedTrade.txType === 'add_liquidity' || correctedTrade.txType === 'remove_liquidity') {
                await this.handleLiquidity(correctedTrade);
                continue;
            }

            // 3.3 构建交易数据（对应 Go 的 model.SolanaPoolTransactionKafka）
            const transaction = {
                dexName: correctedTrade.dexName,
                poolAddress: correctedTrade.poolAddress,
                txHash: correctedTrade.txHash,
                txType: correctedTrade.txType,
                slot: correctedTrade.slot,
                instructionIndex: correctedTrade.instructionIndex,
                timestamp: correctedTrade.timestamp,
                token0Amount: correctedTrade.token0Amount,
                token1Amount: correctedTrade.token1Amount,
                traderAddress: correctedTrade.traderAddress,
                token0Address: correctedTrade.token0Address,
                token1Address: correctedTrade.token1Address,
                baseTokenAddress: this.getBaseTokenAddress('Solana', correctedTrade.token0Address, correctedTrade.token1Address),
                token0UnitPrice: this.calculateToken0UnitPrice(correctedTrade.token0Amount, correctedTrade.token1Amount),
                totalValueUSD: correctedTrade.totalValueUSD || "0",
                usdPrice: correctedTrade.usdPrice || "0",
                token0Reserves: correctedTrade.token0Reserve,
                token1Reserves: correctedTrade.token1Reserve,
                dexSource: correctedTrade.dexSource,
                // 添加Go端的其他字段
                createdAt: Math.floor(Date.now() / 1000),
                updatedAt: Math.floor(Date.now() / 1000),
            };

            // 3.3 检查异常交易
            if (this.isUnusualTx(transaction)) {
                continue;
            }

            // 3.4 投递到交易入库 Kafka（对应 Go 的 DataWarehouseService.PushSolanaTransaction）
            await this.broker.publish(KAFKA_TOPICS.SOLANA_DEX_TRANSACTION, transaction);

            // 3.5 实时推送（对应 Go 端的两个 WebSocketService.PublishMessage 调用）
            await this.pushRealTimeMessage(correctedTrade, transaction);
        }

        console.log(`[Trades] 处理完成: ${enrichedTrades.length} 笔交易`);
    }

    // 计算token0单价（对应Go端的token0UnitPrice计算）
    private calculateToken0UnitPrice(token0Amount: string, token1Amount: string): string {
        try {
            const token0 = parseFloat(token0Amount);
            const token1 = parseFloat(token1Amount);
            if (token0 > 0) {
                return (token1 / token0).toFixed(18);
            }
        } catch (error) {
            console.warn('计算token0UnitPrice失败:', error);
        }
        return "0";
    }

    // 修正交易类型（对应Go端的交易类型判断逻辑）
    private correctTradeType(trade: EnrichedTrade): EnrichedTrade {
        // 对于swap交易，根据买入的代币类型来判断是buy还是sell
        if (trade.txType === 'buy' || trade.txType === 'sell') {
            // 如果用户买入的是稳定币或原生币，就是卖出token操作
            // 这里需要根据具体的交易逻辑来判断用户实际买入的是哪个token

            // 简化逻辑：如果token0是稳定币/原生币且用户获得了token0，则为sell
            // 如果token1是稳定币/原生币且用户获得了token1，则为sell
            // 否则为buy

            let correctedTxType: 'buy' | 'sell' = trade.txType;

            // 这里需要更复杂的逻辑来判断用户实际的交易方向
            // 暂时保持原有逻辑

            return {
                ...trade,
                txType: correctedTxType
            };
        }

        return trade;
    }

    public async handleNewPools(pools: SolanaDexNewPool[]) {
        if (pools.length === 0) return;

        for (const pool of pools) {
            // 1. 构建池子对象（对应 Go 的 model.SolanaPoolPair）
            const poolPair = {
                pairAddress: pool.pairAddress,
                dexName: pool.dexName,
                pairName: `${pool.token0Address}-${pool.token1Address}`,
                token0Address: pool.token0Address,
                token1Address: pool.token1Address,
                deployerAddress: '', // 可从交易解析获取
                creationTxHash: pool.creationTxHash,
                creationTimestamp: pool.timestamp,
                blockNumber: pool.blockNumber,
                isOfficial: 0,
            };

            // 检查时间戳异常（对应 Go 端的 CreationTimestamp > 9999999999 检查）
            if (poolPair.creationTimestamp > 9999999999) {
                console.warn('XXX CreationTimestamp MeteoraServ.SaveNewPool');
            }

            // 2. 投递到数据库同步 Kafka（对应 Go 的 DataWarehouseService.PushDatabaseSyncSolanaPoolPairs）
            await this.broker.publish(KAFKA_TOPICS.DB_SYNC_SOLANA_POOL_PAIRS, poolPair);

            // 3. 投递到新池入库 Kafka（对应 Go 的 DataWarehouseService.PushNewPool）
            await this.broker.publish(KAFKA_TOPICS.SOLANA_DEX_NEW_POOL, poolPair);

            // 4. 投递到 ES 更新（对应 Go 的 DataWarehouseService.PushTokenInfoEsUpdateTopic）
            const tokenAddress = this.getTokenAddress('Solana', poolPair.token0Address, poolPair.token1Address);
            await this.broker.publish(KAFKA_TOPICS.UPDATE_TOKEN_INFO_ES, {
                chainName: 'Solana',
                tokenAddress: tokenAddress,
            });

            // 5. 新池 WebSocket 推送（对应 Go 的 SocketMessageTopicPumpNewPairs）
            const uuid = this.generateUUID();
            const timestamp = Date.now() * 1000000;

            const newPoolMsg: WebSocketMessage = {
                topicName: WS_MESSAGE_TOPICS.PUMP_NEW_PAIRS,
                socketMessageData: {
                    uuid,
                    timestamp,
                    messageType: WS_MESSAGE_TOPICS.PUMP_NEW_PAIRS,
                    data: {
                        name: '', // 需要从token metadata获取
                        symbol: '', // 需要从token metadata获取
                        address: pool.pairAddress,
                        logo: '',
                        decimal: 9,
                        metadataURL: '',
                        createTime: pool.timestamp,
                        creator: '',
                        holders: 1,
                        mcap: 0,
                        vol: 0,
                        progress: 0,
                        tokenInfo: {
                            token0Address: pool.token0Address,
                            token1Address: pool.token1Address,
                        },
                    },
                },
            };

            await this.broker.publishWebSocket(newPoolMsg);

            console.log(`[NewPool] 新池创建: ${pool.pairAddress}`);
        }

        console.log(`[NewPools] 处理完成: ${pools.length} 个新池`);
    }
}