/**
 * Constants module exports
 * Provides a clean interface for all chain and coin related functions
 */

// Export all chain coin related functions and constants
export {
    ChainName,
    NATIVE_COIN_FAKE_ADDRESS,
    SOL_ADDRESS,
    WRAPPED_SOL_ADDRESS,
    WRAPPED_BNB_ADDRESS,
    WRAPPED_ETH_ADDRESS,
    WRAPPED_BASE_ETH_ADDRESS,
    OKX_EVM_NATIVE_COIN_ADDRESS,
    SOL_TOKEN_NAME,
    SOL_TOKEN_SYMBOL,
    SOL_TOKEN_DECIMAL,
    SOL_LOGO_URL,
    WRAPPED_SOL_TOKEN_NAME,
    WRAPPED_SOL_TOKEN_SYMBOL,
    WRAPPED_SOL_TOKEN_DECIMAL,
    WRAPPED_SOL_LOGO_URL,
    BNB_TOKEN_NAME,
    BNB_TOKEN_SYMBOL,
    BNB_TOKEN_DECIMALS,
    BNB_LOGO_URL,
    WRAPPED_BNB_TOKEN_NAME,
    WRAPPED_BNB_TOKEN_SYMBOL,
    WRAPPED_BNB_TOKEN_DECIMALS,
    WRAPPED_BNB_LOGO_URL,
    CHAIN_MAP,
    CHAIN_NATIVE_COIN_DECIMALS_MAP,
    CHAIN_NATIVE_COIN_MAP,
    CHAIN_NATIVE_COIN_SYMBOL,
    CHAIN_NATIVE_COIN_INFO_MAP,
    CoinInfo,
    isChainSupport,
    getSupportChains,
    getChainNativeCoinInfo,
    isChainNativeCoin,
    isChainNativeOrStableCoin,
    getBaseTokenAddress
} from './chainCoin';

// Export all stable coin related functions and constants
export {
    SOL_USDC_ADDRESS,
    STABLE_COIN_MAP,
    isChainStableCoin,
    isStableCoin,
    getChainStableCoinInfo
} from './stableCoin';

// Re-export the main function for convenience
import { isChainNativeCoin } from './chainCoin';
import { isChainStableCoin } from './stableCoin';
import { ChainName } from './chainCoin';

/**
 * Main function: Check if an address is either a native coin or stable coin for the given chain
 * This is the primary function that should be used by external code
 */
export function IsChainNativeOrStableCoin(chainName: ChainName, address: string): boolean {
    return isChainNativeCoin(chainName, address) || isChainStableCoin(chainName, address);
}

/**
 * Utility function to convert string chain name to ChainName enum
 * Handles case insensitive conversion
 */
export function parseChainName(chainName: string): ChainName | null {
    const normalizedChainName = chainName.toLowerCase();
    
    switch (normalizedChainName) {
        case 'solana':
        case 'sol':
            return ChainName.Solana;
        case 'ethereum':
        case 'eth':
            return ChainName.Ethereum;
        case 'bnbchain':
        case 'bsc':
        case 'binance':
            return ChainName.BnbChain;
        case 'base':
            return ChainName.Base;
        default:
            return null;
    }
}

/**
 * Type guard to check if a string is a valid ChainName
 */
export function isValidChainName(chainName: string): chainName is ChainName {
    return Object.values(ChainName).includes(chainName as ChainName);
}
