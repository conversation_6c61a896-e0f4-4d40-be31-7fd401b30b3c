/**
 * Stable coin related constants and functions
 * TypeScript implementation of Go's internal/common/consts/stable_coin.go
 */

import { ChainName, CoinInfo } from './chainCoin';

// SOL USDC Address (Deprecated)
export const SOL_USDC_ADDRESS = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';

// Stable coin mapping for each chain
export const STABLE_COIN_MAP: Record<ChainName, Record<string, CoinInfo>> = {
    [ChainName.Solana]: {
        // USDC 90% market share
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': {
            chainName: ChainName.Solana,
            address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
            name: 'USD Coin',
            symbol: 'USDC',
            decimals: 6,
            logoURL: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/logo.png'
        },
        // USDT
        'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': {
            chainName: ChainName.Solana,
            address: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
            name: 'USDT',
            symbol: 'USDT',
            decimals: 6,
            logoURL: 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB/logo.svg'
        },
        // USDS
        'USDSwr9ApdHk5bvJKMjzff41FfuX8bSxdKcR81vTwcA': {
            chainName: ChainName.Solana,
            address: 'USDSwr9ApdHk5bvJKMjzff41FfuX8bSxdKcR81vTwcA',
            name: 'USDS',
            symbol: 'USDS',
            decimals: 6,
            logoURL: 'https://ipfs.io/ipfs/QmTW9HWfb2wsQqEVJiixkQ73Nsfp2Rx4ESaDSiQ7ThwnFM'
        },
        // PYUSD
        '2b1kV6DkPAnxd5ixfnxCpjxmKwqjjaYmCZfHsFu24GXo': {
            chainName: ChainName.Solana,
            address: '2b1kV6DkPAnxd5ixfnxCpjxmKwqjjaYmCZfHsFu24GXo',
            name: 'PayPal USD',
            symbol: 'PYUSD',
            decimals: 6,
            logoURL: 'https://424565.fs1.hubspotusercontent-na1.net/hubfs/424565/PYUSDLOGO.png'
        },
        // USDG
        '2u1tszSeqZ3qBWF3uNGPFc8TzMk2tdiwknnRMWGWjGWH': {
            chainName: ChainName.Solana,
            address: '2u1tszSeqZ3qBWF3uNGPFc8TzMk2tdiwknnRMWGWjGWH',
            name: 'Global Dollar',
            symbol: 'USDG',
            decimals: 6,
            logoURL: 'https://424565.fs1.hubspotusercontent-na1.net/hubfs/424565/GDN-USDG-Token-512x512.png'
        }
    },
    [ChainName.BnbChain]: {
        // USDT
        '******************************************': {
            chainName: ChainName.BnbChain,
            address: '******************************************',
            name: 'Tether USD',
            symbol: 'USDT',
            decimals: 18,
            logoURL: 'https://tether.to/images/logoCircle.svg'
        },
        // BUSD
        '******************************************': {
            chainName: ChainName.BnbChain,
            address: '******************************************',
            name: 'BUSD Token',
            symbol: 'BUSD',
            decimals: 18,
            logoURL: 'https://images.unicornx.ai/token_icon/bsc/******************************************.svg'
        },
        // USDC
        '******************************************': {
            chainName: ChainName.BnbChain,
            address: '******************************************',
            name: 'USD Coin',
            symbol: 'USDC',
            decimals: 18,
            logoURL: 'https://images.unicornx.ai/token_icon/bsc/******************************************.svg'
        },
        // DAI
        '******************************************': {
            chainName: ChainName.BnbChain,
            address: '******************************************',
            name: 'Dai Token',
            symbol: 'DAI',
            decimals: 18,
            logoURL: 'https://images.unicornx.ai/token_icon/bsc/******************************************.svg'
        },
        // USD1
        '0x8d0d000ee44948fc98c9b98a4fa4921476f08b0d': {
            chainName: ChainName.BnbChain,
            address: '0x8d0d000ee44948fc98c9b98a4fa4921476f08b0d',
            name: 'World Liberty Financial USD',
            symbol: 'USD1',
            decimals: 18,
            logoURL: 'https://s2.coinmarketcap.com/static/img/coins/128x128/36148.png'
        },
        // FDUSD
        '******************************************': {
            chainName: ChainName.BnbChain,
            address: '******************************************',
            name: 'First Digital USD',
            symbol: 'FDUSD',
            decimals: 18,
            logoURL: 'https://images.unicornx.ai/token_icon/bsc/******************************************.svg'
        }
    },
    [ChainName.Ethereum]: {},
    [ChainName.Base]: {}
};

/**
 * Check if an address is a stable coin for the given chain
 * Handles case sensitivity properly for different chains
 */
export function isChainStableCoin(chainName: ChainName, address: string): boolean {
    const stableCoin = STABLE_COIN_MAP[chainName];
    if (!stableCoin) {
        return false;
    }

    switch (chainName) {
        case ChainName.Solana:
            // Solana addresses are case sensitive
            return address in stableCoin;
        default:
            // EVM chains are case insensitive
            return address.toLowerCase() in stableCoin;
    }
}

/**
 * Check if an address is a stable coin across all chains
 */
export function isStableCoin(address: string): boolean {
    for (const chainName of Object.values(ChainName)) {
        const stableCoin = STABLE_COIN_MAP[chainName];
        switch (chainName) {
            case ChainName.Solana:
                if (address in stableCoin) {
                    return true;
                }
                break;
            default:
                if (address.toLowerCase() in stableCoin) {
                    return true;
                }
                break;
        }
    }
    return false;
}

/**
 * Get chain stable coin info
 */
export function getChainStableCoinInfo(chainName: ChainName, address: string): CoinInfo | null {
    const stableCoin = STABLE_COIN_MAP[chainName];
    if (!stableCoin) {
        return null;
    }

    switch (chainName) {
        case ChainName.Solana:
            return stableCoin[address] || null;
        default:
            return stableCoin[address.toLowerCase()] || null;
    }
}
