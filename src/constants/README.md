# Chain Coin Constants

This module provides TypeScript implementations of the Go backend's chain and coin related functions, specifically the `IsChainNativeOrStableCoin` function and related utilities.

## Overview

The module is a direct port of the Go code from:
- `internal/common/consts/chain_coin.go`
- `internal/common/consts/stable_coin.go`

It maintains the same behavior, including case sensitivity rules and supported chains/tokens.

## Files Structure

```
src/constants/
├── chainCoin.ts      # Native coin constants and functions
├── stableCoin.ts     # Stable coin constants and functions
├── index.ts          # Main exports and utility functions
└── README.md         # This documentation
```

## Main Functions

### `IsChainNativeOrStableCoin(chainName: ChainName, address: string): boolean`

The primary function that checks if a token address is either a native coin or stable coin for the given chain.

```typescript
import { IsChainNativeOrStableCoin, ChainName } from './constants';

// Check if USDC is native or stable coin on Solana
const isNativeOrStable = IsChainNativeOrStableCoin(
    ChainName.Solana, 
    'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'
); // returns true
```

### `isChainNativeCoin(chainName: ChainName, address: string): boolean`

Checks if an address is a native coin for the given chain.

```typescript
import { isChainNativeCoin, ChainName } from './constants';

// Check if address is SOL
const isSOL = isChainNativeCoin(
    ChainName.Solana, 
    'So********************************111111112'
); // returns true
```

### `isChainStableCoin(chainName: ChainName, address: string): boolean`

Checks if an address is a stable coin for the given chain.

```typescript
import { isChainStableCoin, ChainName } from './constants';

// Check if address is USDC on Solana
const isUSDC = isChainStableCoin(
    ChainName.Solana, 
    'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'
); // returns true
```

## Supported Chains

- **Solana** (`ChainName.Solana`)
- **Ethereum** (`ChainName.Ethereum`)
- **BNB Chain** (`ChainName.BnbChain`)
- **Base** (`ChainName.Base`)

## Supported Tokens

### Solana Native Coins
- `********************************` (SOL)
- `So********************************111111112` (Wrapped SOL)
- `******************************************` (Virtual native coin)

### Solana Stable Coins
- `EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v` (USDC)
- `Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB` (USDT)
- `USDSwr9ApdHk5bvJKMjzff41FfuX8bSxdKcR81vTwcA` (USDS)
- `2b1kV6DkPAnxd5ixfnxCpjxmKwqjjaYmCZfHsFu24GXo` (PYUSD)
- `2u1tszSeqZ3qBWF3uNGPFc8TzMk2tdiwknnRMWGWjGWH` (USDG)

### BNB Chain Native Coins
- `******************************************` (Wrapped BNB)
- `******************************************` (OKX EVM native coin)
- `******************************************` (Virtual native coin)

### BNB Chain Stable Coins
- `******************************************` (USDT)
- `******************************************` (BUSD)
- `******************************************` (USDC)
- `******************************************` (DAI)
- `******************************************` (USD1)
- `******************************************` (FDUSD)

## Case Sensitivity

**Important**: The implementation maintains the same case sensitivity rules as the Go backend:

- **Solana**: Addresses are **case-sensitive**
- **EVM Chains** (Ethereum, BNB Chain, Base): Addresses are **case-insensitive**

```typescript
// Solana - case sensitive
isChainStableCoin(ChainName.Solana, 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'); // ✅ true
isChainStableCoin(ChainName.Solana, 'epjfwdd5aufqssqem2qn1xzybapC8G4wEGGkZwyTDt1v'); // ❌ false

// BNB Chain - case insensitive
isChainStableCoin(ChainName.BnbChain, '******************************************'); // ✅ true
isChainStableCoin(ChainName.BnbChain, '******************************************'); // ✅ true
```

## Utility Functions

### `parseChainName(chainName: string): ChainName | null`

Converts string chain names to ChainName enum (case-insensitive).

```typescript
parseChainName('solana'); // ChainName.Solana
parseChainName('SOLANA'); // ChainName.Solana
parseChainName('bsc'); // ChainName.BnbChain
parseChainName('invalid'); // null
```

### `getBaseTokenAddress(chainName: ChainName, token0: string, token1: string): string`

Returns the base token address from a pair, prioritizing native coins over stable coins.

```typescript
getBaseTokenAddress(
    ChainName.Solana,
    'So********************************111111112', // SOL
    'SomeOtherToken'
); // returns 'SomeOtherToken' (non-native token)
```

## Integration with DataHandler

The existing `dataHandler.ts` has been updated to use these new functions:

```typescript
// Old implementation (hardcoded)
private isChainNativeOrStableCoin(chainName: string, tokenAddress: string): boolean {
    if (chainName.toLowerCase() === 'solana') {
        return tokenAddress === this.SOLANA_NATIVE ||
               tokenAddress === this.USDC_ADDRESS ||
               tokenAddress === this.USDT_ADDRESS;
    }
    return false;
}

// New implementation (using constants)
private isChainNativeOrStableCoin(chainName: string, tokenAddress: string): boolean {
    const { IsChainNativeOrStableCoin, parseChainName } = require('./constants');
    const parsedChainName = parseChainName(chainName);
    if (!parsedChainName) {
        return false;
    }
    return IsChainNativeOrStableCoin(parsedChainName, tokenAddress);
}
```

## Testing

Run the test suite to verify the implementation:

```bash
npx ts-node src/test/chainCoin.test.ts
```

See usage examples:

```bash
npx ts-node src/examples/usage.ts
```

## Type Safety

The module provides full TypeScript type safety:

```typescript
// ChainName enum prevents invalid chain names
IsChainNativeOrStableCoin(ChainName.Solana, address); // ✅ Type safe

// parseChainName handles string inputs safely
const chain = parseChainName(userInput);
if (chain) {
    IsChainNativeOrStableCoin(chain, address); // ✅ Type safe
}
```
