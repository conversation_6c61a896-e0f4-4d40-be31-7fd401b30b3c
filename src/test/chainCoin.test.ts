/**
 * Test file for chain coin functions
 * Tests the TypeScript implementation against expected Go behavior
 */

import {
    <PERSON><PERSON><PERSON>,
    Is<PERSON>hainNativeOr<PERSON><PERSON><PERSON><PERSON><PERSON>,
    is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    parse<PERSON><PERSON><PERSON><PERSON><PERSON>,
    getB<PERSON><PERSON><PERSON><PERSON>ddress,
    WRAPPED_SOL_ADDRESS,
    <PERSON><PERSON>_ADDRESS,
    SOL_USDC_ADDRESS
} from '../constants';

// Test data based on Go implementation
const testCases = {
    solana: {
        nativeCoins: [
            '11111111111111111111111111111111', // SOL_ADDRESS
            'So11111111111111111111111111111111111111112', // WRAPPED_SOL_ADDRESS
            '0x0000000000000000000000000000000000000000' // NATIVE_COIN_FAKE_ADDRESS
        ],
        stableCoins: [
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
            'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', // USDT
            'USDSwr9ApdHk5bvJKMjzff41FfuX8bSxdKcR81vTwcA', // USDS
            '2b1kV6DkPAnxd5ixfnxCpjxmKwqjjaYmCZfHsFu24GXo', // PYUSD
            '2u1tszSeqZ3qBWF3uNGPFc8TzMk2tdiwknnRMWGWjGWH'  // USDG
        ],
        invalidAddresses: [
            'so11111111111111111111111111111111111111112', // lowercase (case sensitive)
            'epjfwdd5aufqssqem2qn1xzybapC8G4wEGGkZwyTDt1v', // lowercase USDC
            '1111MFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', // invalid
            ''
        ]
    },
    bnbChain: {
        nativeCoins: [
            '0xbb4cdb9cbd36b01bd1cbaebf2de08d9173bc095c', // WRAPPED_BNB_ADDRESS
            '0xBB4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c', // uppercase version
            '0xeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee', // OKX_EVM_NATIVE_COIN_ADDRESS
            '0x0000000000000000000000000000000000000000'  // NATIVE_COIN_FAKE_ADDRESS
        ],
        stableCoins: [
            '0x55d398326f99059ff775485246999027b3197955', // USDT
            '0x55D398326f99059fF775485246999027B3197955', // uppercase USDT
            '0xe9e7cea3dedca5984780bafc599bd69add087d56', // BUSD
            '0x8ac76a51cc950d9822d68b83fe1ad97b32cd580d', // USDC
            '0x1af3f329e8be154074d8769d1ffa4ee058b1dbc3', // DAI
            '0x8d0d000ee44948fc98c9b98a4fa4921476f08b0d', // USD1
            '0xc5f0f7b66764f6ec8c8dff7ba683102295e16409'  // FDUSD
        ],
        invalidAddresses: [
            '******************************************', // invalid
            ''
        ]
    }
};

function runTests() {
    console.log('🧪 Running Chain Coin Tests...\n');

    // Test parseChainName function
    console.log('📋 Testing parseChainName function:');
    const chainNameTests = [
        { input: 'solana', expected: ChainName.Solana },
        { input: 'SOLANA', expected: ChainName.Solana },
        { input: 'sol', expected: ChainName.Solana },
        { input: 'ethereum', expected: ChainName.Ethereum },
        { input: 'eth', expected: ChainName.Ethereum },
        { input: 'bsc', expected: ChainName.BnbChain },
        { input: 'bnbchain', expected: ChainName.BnbChain },
        { input: 'base', expected: ChainName.Base },
        { input: 'invalid', expected: null }
    ];

    chainNameTests.forEach(test => {
        const result = parseChainName(test.input);
        const passed = result === test.expected;
        console.log(`  ${passed ? '✅' : '❌'} parseChainName('${test.input}') = ${result} (expected: ${test.expected})`);
    });

    // Test Solana native coins
    console.log('\n📋 Testing Solana native coins:');
    testCases.solana.nativeCoins.forEach(address => {
        const result = isChainNativeCoin(ChainName.Solana, address);
        console.log(`  ${result ? '✅' : '❌'} isChainNativeCoin(Solana, '${address}') = ${result}`);
    });

    // Test Solana stable coins
    console.log('\n📋 Testing Solana stable coins:');
    testCases.solana.stableCoins.forEach(address => {
        const result = isChainStableCoin(ChainName.Solana, address);
        console.log(`  ${result ? '✅' : '❌'} isChainStableCoin(Solana, '${address}') = ${result}`);
    });

    // Test Solana IsChainNativeOrStableCoin
    console.log('\n📋 Testing Solana IsChainNativeOrStableCoin:');
    [...testCases.solana.nativeCoins, ...testCases.solana.stableCoins].forEach(address => {
        const result = IsChainNativeOrStableCoin(ChainName.Solana, address);
        console.log(`  ${result ? '✅' : '❌'} IsChainNativeOrStableCoin(Solana, '${address}') = ${result}`);
    });

    // Test Solana invalid addresses
    console.log('\n📋 Testing Solana invalid addresses:');
    testCases.solana.invalidAddresses.forEach(address => {
        const result = IsChainNativeOrStableCoin(ChainName.Solana, address);
        console.log(`  ${!result ? '✅' : '❌'} IsChainNativeOrStableCoin(Solana, '${address}') = ${result} (should be false)`);
    });

    // Test BNB Chain native coins
    console.log('\n📋 Testing BNB Chain native coins:');
    testCases.bnbChain.nativeCoins.forEach(address => {
        const result = isChainNativeCoin(ChainName.BnbChain, address);
        console.log(`  ${result ? '✅' : '❌'} isChainNativeCoin(BnbChain, '${address}') = ${result}`);
    });

    // Test BNB Chain stable coins
    console.log('\n📋 Testing BNB Chain stable coins:');
    testCases.bnbChain.stableCoins.forEach(address => {
        const result = isChainStableCoin(ChainName.BnbChain, address);
        console.log(`  ${result ? '✅' : '❌'} isChainStableCoin(BnbChain, '${address}') = ${result}`);
    });

    // Test BNB Chain IsChainNativeOrStableCoin
    console.log('\n📋 Testing BNB Chain IsChainNativeOrStableCoin:');
    [...testCases.bnbChain.nativeCoins, ...testCases.bnbChain.stableCoins].forEach(address => {
        const result = IsChainNativeOrStableCoin(ChainName.BnbChain, address);
        console.log(`  ${result ? '✅' : '❌'} IsChainNativeOrStableCoin(BnbChain, '${address}') = ${result}`);
    });

    // Test getBaseTokenAddress function
    console.log('\n📋 Testing getBaseTokenAddress function:');
    const baseTokenTests = [
        {
            chain: ChainName.Solana,
            token0: WRAPPED_SOL_ADDRESS,
            token1: 'SomeOtherToken123',
            expected: 'SomeOtherToken123'
        },
        {
            chain: ChainName.Solana,
            token0: 'SomeOtherToken123',
            token1: SOL_USDC_ADDRESS,
            expected: 'SomeOtherToken123'
        },
        {
            chain: ChainName.Solana,
            token0: 'TokenA',
            token1: 'TokenB',
            expected: 'TokenA' // default fallback
        }
    ];

    baseTokenTests.forEach((test, index) => {
        const result = getBaseTokenAddress(test.chain, test.token0, test.token1);
        const passed = result === test.expected;
        console.log(`  ${passed ? '✅' : '❌'} Test ${index + 1}: getBaseTokenAddress(${test.chain}, '${test.token0}', '${test.token1}') = '${result}' (expected: '${test.expected}')`);
    });

    console.log('\n🎉 Tests completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests();
}

export { runTests };
