"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.idlConfigManager = exports.IDLConfigManager = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
/**
 * IDL配置管理器
 */
class IDLConfigManager {
    constructor(configPath = './src/parsers/idl/configs') {
        this.configMap = new Map();
        this.loadConfigs(configPath);
    }
    /**
     * 加载配置文件
     */
    loadConfigs(configPath) {
        try {
            const configFile = path_1.default.join(configPath, 'dex-configs.json');
            if (fs_1.default.existsSync(configFile)) {
                const data = fs_1.default.readFileSync(configFile, 'utf-8');
                const configs = JSON.parse(data);
                configs.forEach(config => {
                    this.configMap.set(config.programId, config);
                });
                console.log(`IDL配置加载了 ${configs.length} 个DEX`);
            }
        }
        catch (error) {
            console.error('IDL配置加载失败:', error);
        }
    }
    /**
     * 根据programId获取配置
     */
    getConfig(programId) {
        return this.configMap.get(programId) || null;
    }
}
exports.IDLConfigManager = IDLConfigManager;
exports.idlConfigManager = new IDLConfigManager();
