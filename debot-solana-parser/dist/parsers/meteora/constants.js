"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.METEORA_PROGRAM_IDS = exports.METEORA_DAMM_V2_PROGRAM_ID = exports.METEORA_DAMM_V1_PROGRAM_ID = exports.METEORA_DLMM_PROGRAM_ID = void 0;
/**
 * Meteora DLMM
 */
exports.METEORA_DLMM_PROGRAM_ID = 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo';
/**
 * Meteora DAMM V1
 */
exports.METEORA_DAMM_V1_PROGRAM_ID = 'Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB';
/**
 * Meteora DAMM V2
 */
exports.METEORA_DAMM_V2_PROGRAM_ID = 'cpamdpZCGKUy5JxQXB4dcpGPiikHawvSWAd6mEn1sGG';
/**
 * 一个包含所有需要订阅的 Meteora 程序ID的数组。
 * 这使得在主程序中注册所有这些ID变得容易。
 */
exports.METEORA_PROGRAM_IDS = [
    // METEORA_DLMM_PROGRAM_ID,
    exports.METEORA_DAMM_V1_PROGRAM_ID,
];
