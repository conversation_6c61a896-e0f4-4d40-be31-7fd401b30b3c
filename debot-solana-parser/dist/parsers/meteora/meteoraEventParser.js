"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MeteoraEventParser = void 0;
const anchor_1 = require("@coral-xyz/anchor");
const idlConfigManager_1 = require("../idl/idlConfigManager");
const anchor = __importStar(require("@coral-xyz/anchor"));
const bs58_1 = __importDefault(require("bs58"));
const anchor_2 = require("@coral-xyz/anchor");
const web3_js_1 = require("@solana/web3.js");
/**
 * 简化的 Meteora 事件解析器
 * 直接从交易日志中解析 Anchor 事件，而不是复杂的指令解析
 */
class MeteoraEventParser {
    constructor(supportedPrograms) {
        this.idlCache = new Map();
        this.programCache = new Map();
        this.supportedPrograms = new Set(supportedPrograms);
        // 创建一个简单的provider用于解码（不需要真实连接）
        const dummyConnection = new web3_js_1.Connection('https://api.mainnet-beta.solana.com');
        const dummyWallet = new anchor_2.Wallet(web3_js_1.Keypair.generate());
        this.provider = new anchor_2.AnchorProvider(dummyConnection, dummyWallet, {});
        console.log(`[简化事件解析器] 初始化完成，支持 ${supportedPrograms.length} 个程序`);
    }
    /**
     * 解析交易中的事件 - 简化版本
     */
    async parseTransaction(transaction) {
        const results = [];
        if (transaction.transaction.meta.err != undefined) {
            return results;
        }
        // 检查必要的数据结构
        if (!transaction.transaction.meta?.innerInstructions) {
            return results;
        }
        // 遍历支持的程序
        for (const programIdString of this.supportedPrograms) {
            try {
                // 获取配置和Program
                const config = idlConfigManager_1.idlConfigManager.getConfig(programIdString);
                if (!config)
                    continue;
                const program = await this.getProgram(programIdString);
                if (!program)
                    continue;
                // 计算事件PDA
                const programId = new web3_js_1.PublicKey(programIdString);
                const [eventPDA] = web3_js_1.PublicKey.findProgramAddressSync([Buffer.from("__event_authority")], programId);
                // 找到事件PDA在账户中的索引
                const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];
                const indexOfEventPDA = accountKeys.findIndex((key) => new web3_js_1.PublicKey(key).equals(eventPDA));
                if (indexOfEventPDA === -1)
                    continue;
                // 查找匹配的事件指令
                const matchingInstructions = transaction.transaction.meta.innerInstructions
                    ?.flatMap((ix) => ix.instructions)
                    .filter((instruction) => instruction.accounts.length === 1 &&
                    instruction.accounts[0] === indexOfEventPDA);
                if (!matchingInstructions?.length)
                    continue;
                // 解析事件
                for (const instruction of matchingInstructions) {
                    try {
                        const ixData = anchor.utils.bytes.bs58.decode(bs58_1.default.encode(instruction.data));
                        const eventData = anchor.utils.bytes.base64.encode(ixData.slice(8));
                        // 使用Program.coder.events.decode替代BorshCoder.events.decode
                        const event = program.coder.events.decode(eventData);
                        if (event) {
                            console.log(`✅ 解析到事件: ${event.name}`);
                            console.log(event);
                            const standardEvent = this.createStandardEvent(transaction, event, config);
                            if (standardEvent) {
                                results.push(standardEvent);
                            }
                        }
                    }
                    catch (error) {
                        console.log("error", error);
                    }
                }
            }
            catch (error) {
                console.error(`解析程序 ${programIdString} 失败:`, error);
            }
        }
        return results;
    }
    /**
     * 将 Anchor 事件转换为标准化事件对象
     */
    createStandardEvent(transaction, anchorEvent, config) {
        const eventName = anchorEvent.name;
        const eventData = anchorEvent.data;
        try {
            // 根据事件名称创建对应的标准化对象
            switch (eventName) {
                case 'Swap':
                    return this.createTradeFromSwapEvent(transaction, eventData, config);
                case 'AddLiquidity':
                    return this.createTradeFromLiquidityEvent(transaction, eventData, config, 'add_liquidity');
                case 'RemoveLiquidity':
                    return this.createTradeFromLiquidityEvent(transaction, eventData, config, 'remove_liquidity');
                case 'InitializeLbPair':
                    return this.createPoolFromInitEvent(transaction, eventData, config);
                default:
                    return null;
            }
        }
        catch (error) {
            console.error(`💥 创建标准化事件失败:`, error);
            return null;
        }
    }
    /**
     * 从 Swap 事件创建交易对象
     * 数据映射: eventData.lbPair->poolAddress, eventData.from->traderAddress
     * BN转换: 使用Anchor的BN.toString()直接转换大数
     */
    createTradeFromSwapEvent(transaction, eventData, config) {
        console.log("Swap事件数据:", eventData);
        // 使用Anchor BN直接转换
        const amountIn = new anchor_1.BN(eventData.amountIn).toString();
        const amountOut = new anchor_1.BN(eventData.amountOut).toString();
        // 根据swapForY判断交易方向
        const isSwapForY = eventData.swapForY;
        const txType = isSwapForY ? 'buy' : 'sell';
        // 根据交易方向分配代币数量
        const token0Amount = isSwapForY ? amountIn : amountOut;
        const token1Amount = isSwapForY ? amountOut : amountIn;
        return {
            dexName: config.name,
            poolAddress: eventData.lbPair.toString(),
            txHash: bs58_1.default.encode(transaction.transaction.transaction.signatures),
            txType,
            slot: transaction.slot || 0,
            instructionIndex: '0',
            timestamp: Math.floor(Date.now() / 1000),
            traderAddress: eventData.from.toString(),
            token0Address: 'unknown', // 需要从池配置获取
            token1Address: 'unknown', // 需要从池配置获取
            token0Amount,
            token1Amount,
            token0Reserve: '0',
            token1Reserve: '0',
            dexSource: 'Event-' + config.name,
        };
    }
    /**
     * 从流动性事件创建交易对象
     */
    createTradeFromLiquidityEvent(transaction, eventData, config, txType) {
        return {
            dexName: config.name,
            poolAddress: eventData.lbPair || eventData.pool || 'unknown',
            txHash: bs58_1.default.encode(transaction.transaction.transaction.signatures),
            txType,
            slot: transaction.slot || 0,
            instructionIndex: '0',
            timestamp: Math.floor(Date.now() / 1000),
            traderAddress: eventData.from || eventData.user || 'unknown',
            token0Address: 'unknown', // 需要从池信息获取
            token1Address: 'unknown',
            token0Amount: eventData.amounts?.[0]?.toString() || '0',
            token1Amount: eventData.amounts?.[1]?.toString() || '0',
            token0Reserve: '0',
            token1Reserve: '0',
            dexSource: 'Event-' + config.name,
        };
    }
    /**
     * 从初始化事件创建新池对象
     */
    createPoolFromInitEvent(transaction, eventData, config) {
        return {
            dexName: config.name,
            pairAddress: eventData.lbPair || eventData.pool || 'unknown',
            token0Address: eventData.tokenX || eventData.tokenA || 'unknown',
            token1Address: eventData.tokenY || eventData.tokenB || 'unknown',
            creationTxHash: bs58_1.default.encode(transaction.transaction.transaction.signatures),
            blockNumber: transaction.slot || 0,
            timestamp: Math.floor(Date.now() / 1000),
        };
    }
    // 复用原有的 IDL 和 Coder 管理方法
    async getIDL(config) {
        if (this.idlCache.has(config.programId)) {
            return this.idlCache.get(config.programId);
        }
        try {
            // Dynamically import the TypeScript module directly
            const idlModule = await Promise.resolve(`${config.path}`).then(s => __importStar(require(s)));
            // Access the exported IDL constant
            const idl = idlModule.IDL;
            if (!idl) {
                console.error(`IDL not found in module: ${config.path}`);
                return null;
            }
            this.idlCache.set(config.programId, idl);
            return idl;
        }
        catch (error) {
            console.error(`Error loading IDL from ${config.path}:`, error);
            return null;
        }
    }
    async getProgram(programIdString) {
        if (this.programCache.has(programIdString)) {
            return this.programCache.get(programIdString);
        }
        const config = idlConfigManager_1.idlConfigManager.getConfig(programIdString);
        if (!config) {
            console.error(`未找到 programId=${programIdString} 的配置`);
            return null;
        }
        const idl = await this.getIDL(config);
        if (!idl) {
            console.error(`IDL 加载失败 for programId=${programIdString}`);
            return null;
        }
        const program = new anchor_2.Program(idl, new web3_js_1.PublicKey(programIdString).toBase58(), this.provider);
        this.programCache.set(programIdString, program);
        return program;
    }
}
exports.MeteoraEventParser = MeteoraEventParser;
