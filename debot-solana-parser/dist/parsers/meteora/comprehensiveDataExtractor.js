"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComprehensiveMeteoraDataExtractor = void 0;
const anchor = __importStar(require("@coral-xyz/anchor"));
const web3_js_1 = require("@solana/web3.js");
const idlConfigManager_1 = require("../idl/idlConfigManager");
const messageQueueService_1 = require("../../services/messageQueueService");
const bs58_1 = __importDefault(require("bs58"));
const util_1 = __importDefault(require("util"));
/**
 * 利用 IDL 的所有数据源：instructions、accounts、events、types、errors
 * 完全基于IDL动态解析，无硬编码指令索引，整合消息队列投递
 * 实现了IAdvancedParser接口，可作为其他DEX解析器的参考实现
 */
class ComprehensiveMeteoraDataExtractor {
    constructor(supportedPrograms, messageQueueConfig, rpcUrl) {
        this.programCache = new Map();
        this.supportedPrograms = supportedPrograms;
        this.connection = new web3_js_1.Connection(rpcUrl || 'https://api.mainnet-beta.solana.com');
        this.messageQueue = new messageQueueService_1.MessageQueueService(messageQueueConfig);
        console.log(`初始化完成，支持 ${supportedPrograms.length} 个程序`);
    }
    /**
     * 🎯 新增：直接使用IDL解析Pool账户数据
     */
    async parsePoolData(poolAddress, programId) {
        try {
            const poolPubkey = typeof poolAddress === 'string'
                ? new web3_js_1.PublicKey(poolAddress)
                : poolAddress;
            // 如果没有提供programId，则尝试所有支持的程序
            const programIds = programId ? [programId] : this.supportedPrograms;
            for (const programIdStr of programIds) {
                try {
                    const program = await this.getProgram(programIdStr);
                    if (!program)
                        continue;
                    // 直接使用程序的account方法解析pool数据
                    const poolData = await program.account.pool.fetch(poolPubkey);
                    console.log("program.account", program.account);
                    console.log("poolData", poolData);
                    // 解析得到的数据结构完全对应IDL中的pool定义
                    const parsedPool = {
                        // 基础信息
                        address: poolPubkey.toBase58(),
                        lpMint: poolData.lpMint?.toBase58() || '',
                        tokenAMint: poolData.tokenAMint?.toBase58() || '',
                        tokenBMint: poolData.tokenBMint?.toBase58() || '',
                        // Vault信息
                        aVault: poolData.aVault?.toBase58() || '',
                        bVault: poolData.bVault?.toBase58() || '',
                        aVaultLp: poolData.aVaultLp?.toBase58() || '',
                        bVaultLp: poolData.bVaultLp?.toBase58() || '',
                        // 状态信息
                        enabled: Boolean(poolData.enabled),
                        poolType: poolData.poolType,
                        // 费用信息
                        fees: {
                            tradeFeeNumerator: poolData.fees?.tradeFeeNumerator?.toString() || '0',
                            tradeFeeDenominator: poolData.fees?.tradeFeeDenominator?.toString() || '0',
                            protocolTradeFeeNumerator: poolData.fees?.protocolTradeFeeNumerator?.toString() || '0',
                            protocolTradeFeeDenominator: poolData.fees?.protocolTradeFeeDenominator?.toString() || '0'
                        },
                        // 协议费用账户
                        protocolTokenAFee: poolData.protocolTokenAFee?.toBase58() || '',
                        protocolTokenBFee: poolData.protocolTokenBFee?.toBase58() || '',
                        // 曲线类型（重要：决定了这是什么类型的池子）
                        curveType: poolData.curveType,
                        // 锁定LP信息
                        totalLockedLp: poolData.totalLockedLp?.toString() || '0',
                        // 启动配置
                        bootstrapping: poolData.bootstrapping ? {
                            activationPoint: poolData.bootstrapping.activationPoint?.toString() || '0',
                            whitelistedVault: poolData.bootstrapping.whitelistedVault?.toBase58() || '',
                            poolCreator: poolData.bootstrapping.poolCreator?.toBase58() || '',
                            activationType: poolData.bootstrapping.activationType || 0
                        } : undefined,
                        // 合作伙伴信息
                        partnerInfo: poolData.partnerInfo ? {
                            feeNumerator: poolData.partnerInfo.feeNumerator?.toString() || '0',
                            partnerAuthority: poolData.partnerInfo.partnerAuthority?.toBase58() || '',
                            pendingFeeA: poolData.partnerInfo.pendingFeeA?.toString() || '0',
                            pendingFeeB: poolData.partnerInfo.pendingFeeB?.toString() || '0'
                        } : undefined,
                        // 其他字段
                        feeLastUpdatedAt: poolData.feeLastUpdatedAt?.toString() || '0',
                        stake: poolData.stake?.toBase58() || ''
                    };
                    console.log(`✅ 成功解析Pool数据: ${poolPubkey.toBase58()}`);
                    return parsedPool;
                }
                catch (error) {
                    // 如果当前程序无法解析，尝试下一个
                    continue;
                }
            }
            console.warn(`❌ 无法使用任何程序解析Pool: ${poolPubkey.toBase58()}`);
            return null;
        }
        catch (error) {
            console.error('解析Pool数据失败:', error);
            return null;
        }
    }
    /**
     * 🎯 新增：从交易中提取并解析Pool数据
     */
    async extractPoolsFromTransaction(transaction) {
        const pools = [];
        const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];
        // 遍历交易中的所有账户，尝试解析为Pool
        for (const accountKey of accountKeys) {
            try {
                const pubkey = typeof accountKey === 'string'
                    ? new web3_js_1.PublicKey(accountKey)
                    : new web3_js_1.PublicKey(accountKey);
                const poolData = await this.parsePoolData(pubkey);
                if (poolData) {
                    pools.push(poolData);
                }
            }
            catch {
                // 忽略无效的公钥或非Pool账户
            }
        }
        return pools;
    }
    /**
     * 实现IParser接口 - 兼容现有系统
     */
    parse(transaction) {
        // 同步版本，启动异步处理但立即返回
        this.processTransaction(transaction).catch(error => {
            console.error('异步处理失败:', error);
        });
        return []; // 返回空数组，实际结果通过异步处理
    }
    /**
     * 实现IAdvancedParser.processTransaction
     */
    async processTransaction(transaction) {
        const startTime = Date.now();
        try {
            // 如果交易有错误，则跳过处理
            if (transaction.transaction.meta.err) {
                return {
                    success: false,
                    processingTime: Date.now() - startTime,
                    tradesCount: 0,
                    poolsCount: 0,
                    extractedData: null,
                    standardizedData: null,
                    error: '交易执行失败，跳过处理'
                };
            }
            // 1. 完整数据提取（基于IDL）
            const extractedData = await this.extractCompleteData(transaction);
            // 2. 转换为标准格式
            const standardizedData = await this.standardizeData(extractedData, transaction);
            // 3. 投递到消息队列
            const publishResult = await this.publishData(standardizedData);
            return {
                success: true,
                processingTime: Date.now() - startTime,
                tradesCount: publishResult.tradesPublished,
                poolsCount: publishResult.poolsPublished,
                extractedData,
                standardizedData,
                error: null
            };
        }
        catch (error) {
            console.error('处理交易失败:', error);
            return {
                success: false,
                processingTime: Date.now() - startTime,
                tradesCount: 0,
                poolsCount: 0,
                extractedData: null,
                standardizedData: null,
                error: error.message
            };
        }
    }
    /**
     * 数据解析
     */
    async extractCompleteData(transaction) {
        const result = {
            instructions: [],
            accountStates: [],
            metadata: {
                txHash: bs58_1.default.encode(transaction.transaction.signature),
                slot: transaction.slot || 0,
                timestamp: Math.floor(Date.now() / 1000)
            }
        };
        // 1. 提取指令数据
        result.instructions = await this.extractInstructionData(transaction);
        // 2. 提取账户状态数据
        result.accountStates = await this.extractProgramAccountStates(transaction);
        // 3. 🎯 新增：提取Pool数据
        const poolsData = await this.extractPoolsFromTransaction(transaction);
        if (poolsData.length > 0) {
            console.log(`✅ 从交易中提取到 ${poolsData.length} 个Pool数据`);
            // 将Pool数据添加到账户状态中
            for (const pool of poolsData) {
                console.log("pool11111", pool);
                result.accountStates.push({
                    address: pool.address,
                    data: {
                        poolData: pool,
                        accountType: 'METEORA_POOL'
                    },
                    owner: this.supportedPrograms[0] // 使用第一个支持的程序ID
                });
            }
        }
        return result;
    }
    /**
     * 2. 指令数据提取（处理顶层指令和内部指令）
     */
    async extractInstructionData(transaction) {
        const instructions = [];
        const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];
        // 1. 处理顶层指令
        const topLevelInstructions = transaction.transaction?.transaction?.message?.instructions || [];
        for (let i = 0; i < topLevelInstructions.length; i++) {
            const instruction = topLevelInstructions[i];
            const programId = bs58_1.default.encode(accountKeys[instruction.programIdIndex]);
            await this.processInstruction(instruction, programId, accountKeys, i, `top_${i}`, instructions);
        }
        // 2. 处理内部指令 - 这里往往包含实际的DEX操作
        const innerInstructions = transaction.transaction.meta?.innerInstructions || [];
        for (let outerIndex = 0; outerIndex < innerInstructions.length; outerIndex++) {
            const innerInstructionGroup = innerInstructions[outerIndex];
            const innerInstructionList = innerInstructionGroup.instructions || [];
            for (let innerIndex = 0; innerIndex < innerInstructionList.length; innerIndex++) {
                const instruction = innerInstructionList[innerIndex];
                const programId = bs58_1.default.encode(accountKeys[instruction.programIdIndex]);
                await this.processInstruction(instruction, programId, accountKeys, innerIndex, // 使用内部指令的索引作为数字索引
                `inner_${outerIndex}_${innerIndex}`, instructions);
            }
        }
        return instructions;
    }
    /**
     * 处理单个指令（顶层或内部指令）
     */
    async processInstruction(instruction, programId, accountKeys, index, instructionId, instructions) {
        if (this.supportedPrograms.includes(programId)) {
            const program = await this.getProgram(programId);
            if (!program)
                return;
            try {
                const instructionBuffer = Buffer.from(instruction.data);
                const decodedInstruction = this.decodeInstructionData(instructionBuffer, program);
                if (!decodedInstruction) {
                    return;
                }
                const instructionDef = program.idl.instructions.find((inst) => inst.name === decodedInstruction.name);
                if (instructionDef) {
                    const instructionData = await this.buildInstructionData(decodedInstruction, instructionDef, instruction, accountKeys, programId, index, instructionId);
                    instructions.push(instructionData);
                }
            }
            catch (error) {
                console.warn(`❌ 解析指令 ${instructionId} 失败:`, error);
            }
        }
    }
    /**
     * 解码指令数据
     */
    decodeInstructionData(data, program) {
        try {
            if (data.length === 0) {
                console.log("❌ 指令数据为空");
                return null;
            }
            // 使用 Anchor 的 BorshInstructionCoder 解码指令
            const coder = new anchor.BorshInstructionCoder(program.idl);
            const decoded = coder.decode(data);
            if (decoded) {
                // 获取指令定义
                const instructionDef = program.idl.instructions.find((inst) => inst.name === decoded.name);
                return {
                    name: decoded.name,
                    data: decoded.data,
                    definition: instructionDef
                };
            }
            return null;
        }
        catch (error) {
            return null;
        }
    }
    /**
     * 通过 IDL 解析程序特定的账户状态
     */
    async extractProgramAccountStates(transaction) {
        const accountKeys = transaction.transaction?.transaction?.message?.accountKeys || [];
        const instructions = transaction.transaction?.transaction?.message?.instructions || [];
        let accountStates = [];
        for (const programId of this.supportedPrograms) {
            try {
                const program = await this.getProgram(programId);
                if (!program)
                    continue;
                // 获取 IDL 中定义的账户类型
                const accountTypes = program.idl.accounts || [];
                // 查找涉及此程序的指令
                const programInstructions = instructions.filter((ix) => {
                    const ixProgramId = accountKeys[ix.programIdIndex];
                    return (typeof ixProgramId === 'string' ? ixProgramId : bs58_1.default.encode(ixProgramId)) === programId;
                });
                for (const instruction of programInstructions) {
                    // 推断指令涉及的账户可能的类型
                    const instructionAccounts = instruction.accounts || [];
                    console.log("instructionAccounts", bs58_1.default.encode(instruction.accounts));
                    console.log("instructionAccounts", bs58_1.default.encode(instruction.data));
                    for (const accountIndex of instructionAccounts) {
                        const accountKey = accountKeys[accountIndex];
                        const accountAddress = typeof accountKey === 'string' ? accountKey : bs58_1.default.encode(accountKey);
                        accountStates.push({
                            address: accountAddress,
                            data: {
                                instructionContext: instruction,
                            },
                            owner: programId
                        });
                    }
                }
            }
            catch (error) {
                console.warn(`解析程序 ${programId} 的账户状态失败:`, error);
            }
        }
        return accountStates;
    }
    /**
     * 直接基于 IDL 指令定义获取账户类型（无硬编码推断）
     */
    inferAccountType(instruction, accountIndex, accountTypes, program) {
        try {
            // 解码指令获取指令名称
            const instructionBuffer = Buffer.from(instruction.data);
            const decodedInstruction = this.decodeInstructionData(instructionBuffer, program);
            if (!decodedInstruction)
                return null;
            // 直接从 IDL 指令定义中获取账户信息
            const instructionDef = program.idl.instructions.find((inst) => inst.name === decodedInstruction.name);
            if (!instructionDef || !instructionDef.accounts)
                return null;
            // 直接使用 IDL 中定义的账户信息
            const accountDef = instructionDef.accounts[accountIndex];
            if (!accountDef)
                return null;
            // 返回 IDL 中定义的账户信息
            return {
                ...accountDef, // 展开所有 IDL 中的字段
                source: 'IDL_DEFINITION'
            };
        }
        catch (error) {
            return null;
        }
    }
    /**
     * 🎯 转换为标准格式（基于提取的数据）
     */
    async standardizeData(extractedData, transaction) {
        const trades = [];
        const pools = [];
        console.log("extractedData", util_1.default.inspect(extractedData, {
            depth: null, // 无限深度
            colors: true, // 彩色输出（可选）
            compact: false // 格式化输出（可选）
        }));
        // 从指令数据转换
        for (const instruction of extractedData.instructions) {
            const standardized = await this.standardizeInstruction(instruction, extractedData, transaction);
            console.log("standardized", standardized);
            if (standardized) {
                if (standardized.type === 'trade') {
                    trades.push(standardized.data);
                }
                else if (standardized.type === 'pool') {
                    pools.push(standardized.data);
                }
            }
        }
        return { trades, pools };
    }
    /**
     * 标准化指令数据
     */
    async standardizeInstruction(instruction, context, transaction) {
        // 基于指令名称和提取的数据创建标准格式
        const instructionName = instruction.name;
        try {
            if (instructionName === 'swap') {
                return {
                    type: 'trade',
                    data: {
                        dexName: 'Meteora',
                        poolAddress: instruction.accounts.pool,
                        txHash: context.metadata.txHash,
                        txType: 'buy', // 可以基于数据推断
                        slot: context.metadata.slot,
                        instructionIndex: instruction.instructionId,
                        timestamp: context.metadata.timestamp,
                        traderAddress: instruction.accounts.userSourceToken,
                        token0Address: instruction.accounts.aVaultLpMint || instruction.accounts.aVaultLpMint || 'unknown',
                        token1Address: instruction.accounts.bVaultLpMint || instruction.accounts.bVaultLpMint || 'unknown',
                        token0Amount: '0', // 从指令数据中提取
                        token1Amount: '0',
                        token0Reserve: '0',
                        token1Reserve: '0',
                        dexSource: `IDL-Instruction-${instruction.programId.substring(0, 8)}`,
                    }
                };
            }
        }
        catch (error) {
            console.warn('标准化指令失败:', error);
        }
        return null;
    }
    /**
     * 投递数据到消息队列
     */
    async publishData(data) {
        let tradesPublished = 0;
        let poolsPublished = 0;
        try {
            // 发布交易数据
            for (const trade of data.trades) {
                await this.messageQueue.publishTrade(trade);
                tradesPublished++;
            }
            // 发布池子数据
            for (const pool of data.pools) {
                await this.messageQueue.publishNewPool(pool);
                poolsPublished++;
            }
            return { tradesPublished, poolsPublished };
        }
        catch (error) {
            console.error('发布数据失败:', error);
            throw error;
        }
    }
    /**
     * 构建指令数据（核心方法 - 无硬编码索引）
     */
    async buildInstructionData(decodedInstruction, instructionDef, rawInstruction, accountKeys, programId, index, instructionId) {
        // 🔥 关键：自动映射账户（基于IDL定义，无硬编码）
        const accounts = this.mapInstructionAccounts(instructionDef.accounts, rawInstruction.accounts, accountKeys);
        return {
            name: decodedInstruction.name,
            programId,
            index,
            instructionId,
            data: decodedInstruction.data,
            accounts,
        };
    }
    /**
     * 自动映射指令账户（不需要硬编码索引）
     */
    mapInstructionAccounts(accountDefs, accountIndices, accountKeys) {
        const accounts = {};
        accountDefs.forEach((accountDef, index) => {
            if (index < accountIndices.length) {
                const accountIndex = accountIndices[index];
                if (accountIndex < accountKeys.length) {
                    accounts[accountDef.name] = bs58_1.default.encode(accountKeys[accountIndex]);
                }
            }
        });
        return accounts;
    }
    /**
     * 获取程序实例
     */
    async getProgram(programIdString) {
        if (this.programCache.has(programIdString)) {
            return this.programCache.get(programIdString);
        }
        try {
            const config = idlConfigManager_1.idlConfigManager.getConfig(programIdString);
            if (!config)
                return null;
            // 🔥 修复：动态加载IDL
            const idlModule = await Promise.resolve(`${config.path}`).then(s => __importStar(require(s)));
            const idl = idlModule.IDL;
            const programId = new web3_js_1.PublicKey(programIdString);
            // 🔥 修复：创建一个不需要钱包的 provider
            // 对于解析数据，我们不需要发送交易，所以创建一个最小的 provider
            const provider = new anchor.AnchorProvider(this.connection, 
            // 创建一个虚拟钱包，只用于满足 provider 要求
            {
                publicKey: new web3_js_1.PublicKey('11111111111111111111111111111111'), // 系统程序公钥作为占位符
                signTransaction: async () => { throw new Error('Cannot sign transaction in read-only mode'); },
                signAllTransactions: async () => { throw new Error('Cannot sign transaction in read-only mode'); }
            }, { commitment: 'confirmed' });
            const program = new anchor.Program(idl, programId, provider);
            this.programCache.set(programIdString, program);
            return program;
        }
        catch (error) {
            console.error(`创建程序实例失败 ${programIdString}:`, error);
            return null;
        }
    }
    /**
     * 实现IAdvancedParser.initialize
     */
    async initialize() {
        await this.messageQueue.initialize();
        console.log('初始化完成');
    }
}
exports.ComprehensiveMeteoraDataExtractor = ComprehensiveMeteoraDataExtractor;
