"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Dispatcher = void 0;
const bs58_1 = __importDefault(require("bs58"));
const eventemitter3_1 = __importDefault(require("eventemitter3"));
const eventAdapter_1 = require("./parsers/eventAdapter");
const web3_js_1 = require("@solana/web3.js");
/**
 * Dispatcher (调度器) 是可插拔架构的核心。
 */
class Dispatcher extends eventemitter3_1.default {
    constructor() {
        super(...arguments);
        this.parserRegistry = new Map();
    }
    /**
     * 为特定的程序地址注册一个解析器。
     * @param programId - DEX 的程序地址。
     * @param parser - 一个实现了 IParser 接口的类的实例。
     */
    register(programId, parser) {
        if (this.parserRegistry.has(programId)) {
            console.warn(`程序ID ${programId} 的解析器已被注册。即将覆盖。`);
        }
        this.parserRegistry.set(programId, parser);
        const PROGRAM_ID = new web3_js_1.PublicKey(programId);
        if (parser instanceof eventAdapter_1.EventAdapter) {
            parser.onAsyncResults = (trades, newPools) => {
                if (trades.length > 0) {
                    this.emit('trade', trades);
                }
                if (newPools.length > 0) {
                    this.emit('new_pool', newPools);
                }
            };
        }
    }
    /**
     * 接收一笔交易并将其分发给适当的解析器。
     * @param txUpdate - 来自 Geyser 数据流的原始交易更新。
     */
    dispatch(txUpdate) {
        if (!txUpdate.transaction || !txUpdate.transaction.transaction) {
            return; // 不是一个有效的交易更新
        }
        const { transaction } = txUpdate.transaction;
        // 检查数据格式，适配Yellowstone格式
        // 修正：使用正确的Yellowstone数据结构路径
        const rawAccountKeys = transaction.transaction?.message?.accountKeys;
        if (!rawAccountKeys) {
            return;
        }
        // 将账户公钥转换为 Base58 字符串以便查找
        const accountKeys = rawAccountKeys.map((key) => bs58_1.default.encode(key));
        const seenParsers = new Set();
        // 检查顶层指令和内部指令 - 修正数据结构路径
        const allInstructions = [
            ...transaction.transaction.message.instructions,
            ...(transaction.meta?.innerInstructions || []).flatMap((ix) => ix.instructions)
        ];
        for (const instruction of allInstructions) {
            const programIdIndex = instruction.programIdIndex;
            const programId = accountKeys[programIdIndex];
            const parser = this.parserRegistry.get(programId);
            // 如果找到解析器且本次交易尚未调用过此解析器
            if (parser && !seenParsers.has(parser)) {
                const parsedData = parser.parse(txUpdate.transaction);
                if (parsedData) {
                    const trades = parsedData.filter(d => 'txType' in d);
                    const newPools = parsedData.filter(d => 'pairAddress' in d);
                    if (trades.length > 0) {
                        this.emit('trade', trades);
                    }
                    if (newPools.length > 0) {
                        this.emit('new_pool', newPools);
                    }
                }
                seenParsers.add(parser);
            }
        }
    }
}
exports.Dispatcher = Dispatcher;
