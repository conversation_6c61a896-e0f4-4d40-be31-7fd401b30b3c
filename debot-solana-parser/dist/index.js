"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const geyserClient_1 = require("./geyserClient");
const dispatcher_1 = require("./dispatcher");
const dataHandler_1 = require("./dataHandler");
const constants_1 = require("./parsers/meteora/constants");
const messageBroker_1 = require("./services/messageBroker");
const eventAdapter_1 = require("./parsers/eventAdapter");
/**
 * 初始化并运行 DEX 监控服务的主函数 (简化事件解析模式)
 */
async function main() {
    // 1. 初始化消息代理
    const messageBroker = messageBroker_1.MessageBroker.getInstance();
    await messageBroker.connect();
    // 2. 初始化 Dispatcher
    const dispatcher = new dispatcher_1.Dispatcher();
    // 3. 创建并注册事件解析适配器
    const eventAdapter = new eventAdapter_1.EventAdapter();
    // 为每个程序ID注册解析器
    constants_1.METEORA_PROGRAM_IDS.forEach(programId => {
        dispatcher.register(programId, eventAdapter);
    });
    console.log(`已注册 ${constants_1.METEORA_PROGRAM_IDS.length} 个程序到 Dispatcher`);
    // 4. 初始化数据处理器
    const dataHandler = new dataHandler_1.DataHandler();
    // 5. 设置事件监听
    dispatcher.on('trade', (trades) => {
        dataHandler.handleTrades(trades);
    });
    dispatcher.on('new_pool', (newPools) => {
        dataHandler.handleNewPools(newPools);
    });
    // 6. 启动gRPC订阅
    const grpcClient = geyserClient_1.GrpcClient.getInstance();
    grpcClient.subscribe(constants_1.METEORA_PROGRAM_IDS, async (transactionUpdate) => {
        try {
            dispatcher.dispatch(transactionUpdate);
        }
        catch (error) {
            console.error(`交易处理失败:`, error);
        }
    });
}
process.on('SIGINT', async () => {
    try {
        // 关闭消息代理
        console.log('正在关闭消息代理...');
        await messageBroker_1.MessageBroker.getInstance().close();
        process.exit(0);
    }
    catch (error) {
        console.error('关闭过程中发生错误:', error);
        process.exit(1);
    }
});
// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    console.error('未捕获的异常:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason);
    process.exit(1);
});
main().catch(error => {
    console.error('启动简化事件解析服务失败:', error);
    process.exit(1);
});
