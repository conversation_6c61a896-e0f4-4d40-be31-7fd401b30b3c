"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageQueueService = void 0;
const kafkajs_1 = require("kafkajs");
const amqp = __importStar(require("amqplib"));
/**
 * 统一消息队列服务
 * 支持 Kafka 和 RabbitMQ 的数据投递
 */
class MessageQueueService {
    constructor(config) {
        this.config = config;
    }
    /**
     * 初始化连接
     */
    async initialize() {
        if (this.config.kafka.enabled) {
            await this.initializeKafka();
        }
        if (this.config.rabbitmq.enabled) {
            await this.initializeRabbitMQ();
        }
    }
    /**
     * 投递交易数据
     */
    async publishTrade(trade) {
        const message = this.formatTradeMessage(trade);
        await Promise.all([
            this.publishToKafka('solana-dex-trades', message),
            this.publishToRabbitMQ('solana.dex.trades', message)
        ]);
    }
    /**
     * 投递新池数据
     */
    async publishNewPool(pool) {
        const message = this.formatPoolMessage(pool);
        await Promise.all([
            this.publishToKafka('solana-dex-pools', message),
            this.publishToRabbitMQ('solana.dex.pools', message)
        ]);
    }
    /**
     * 批量投递数据
     */
    async publishBatch(trades, pools) {
        const batchMessage = {
            timestamp: Date.now(),
            trades: trades.map(trade => this.formatTradeMessage(trade)),
            pools: pools.map(pool => this.formatPoolMessage(pool)),
            metadata: {
                tradeCount: trades.length,
                poolCount: pools.length,
                source: 'meteora-parser'
            }
        };
        await Promise.all([
            this.publishToKafka('solana-dex-batch', batchMessage),
            this.publishToRabbitMQ('solana.dex.batch', batchMessage)
        ]);
    }
    // ==================== Kafka 相关方法 ====================
    async initializeKafka() {
        try {
            const kafka = new kafkajs_1.Kafka({
                clientId: this.config.kafka.clientId,
                brokers: this.config.kafka.brokers,
                retry: {
                    initialRetryTime: 100,
                    retries: 8
                }
            });
            this.kafkaProducer = kafka.producer({
                maxInFlightRequests: 1,
                idempotent: true,
                transactionTimeout: 30000,
            });
            await this.kafkaProducer.connect();
            console.log('✅ Kafka 连接成功');
        }
        catch (error) {
            console.error('❌ Kafka 连接失败:', error);
        }
    }
    async publishToKafka(topic, message) {
        if (!this.kafkaProducer)
            return;
        try {
            await this.kafkaProducer.send({
                topic,
                messages: [{
                        key: this.generateMessageKey(message),
                        value: JSON.stringify(message),
                        timestamp: Date.now().toString(),
                        headers: {
                            'content-type': 'application/json',
                            'source': 'meteora-parser',
                            'version': '1.0'
                        }
                    }]
            });
            console.log(`📤 Kafka 消息发送成功: ${topic}`);
        }
        catch (error) {
            console.error(`❌ Kafka 消息发送失败 (${topic}):`, error);
        }
    }
    // ==================== RabbitMQ 相关方法 ====================
    async initializeRabbitMQ() {
        try {
            const connection = await amqp.connect(this.config.rabbitmq.url);
            this.rabbitChannel = await connection.createChannel();
            // 声明交换机
            await this.rabbitChannel.assertExchange('solana-dex', 'topic', {
                durable: true
            });
            // 声明队列
            await this.rabbitChannel.assertQueue('solana-dex-trades', { durable: true });
            await this.rabbitChannel.assertQueue('solana-dex-pools', { durable: true });
            await this.rabbitChannel.assertQueue('solana-dex-batch', { durable: true });
            console.log('✅ RabbitMQ 连接成功');
        }
        catch (error) {
            console.error('❌ RabbitMQ 连接失败:', error);
        }
    }
    async publishToRabbitMQ(routingKey, message) {
        if (!this.rabbitChannel)
            return;
        try {
            const messageBuffer = Buffer.from(JSON.stringify(message));
            this.rabbitChannel.publish('solana-dex', routingKey, messageBuffer, {
                persistent: true,
                timestamp: Date.now(),
                headers: {
                    'content-type': 'application/json',
                    'source': 'meteora-parser',
                    'version': '1.0'
                }
            });
            console.log(`📤 RabbitMQ 消息发送成功: ${routingKey}`);
        }
        catch (error) {
            console.error(`❌ RabbitMQ 消息发送失败 (${routingKey}):`, error);
        }
    }
    // ==================== 消息格式化方法 ====================
    formatTradeMessage(trade) {
        return {
            // 基础信息
            id: `${trade.txHash}-${trade.instructionIndex}`,
            txHash: trade.txHash,
            slot: trade.slot,
            timestamp: trade.timestamp,
            // 协议信息
            protocol: 'meteora',
            dexName: trade.dexName,
            dexSource: trade.dexSource,
            // 交易信息
            txType: trade.txType,
            traderAddress: trade.traderAddress,
            // 池子信息
            poolAddress: trade.poolAddress,
            token0Address: trade.token0Address,
            token1Address: trade.token1Address,
            // 数量信息
            token0Amount: trade.token0Amount,
            token1Amount: trade.token1Amount,
            token0Reserve: trade.token0Reserve,
            token1Reserve: trade.token1Reserve,
            // 计算字段
            price: this.calculatePrice(trade.token0Amount, trade.token1Amount),
            volumeUSD: this.calculateVolumeUSD(trade),
            // 元数据
            metadata: {
                instructionIndex: trade.instructionIndex,
                parseSource: 'meteora-event-parser',
                parseTimestamp: Date.now()
            }
        };
    }
    formatPoolMessage(pool) {
        return {
            // 基础信息
            id: `${pool.creationTxHash}-${pool.pairAddress}`,
            txHash: pool.creationTxHash,
            blockNumber: pool.blockNumber,
            timestamp: pool.timestamp,
            // 协议信息
            protocol: 'meteora',
            dexName: pool.dexName,
            // 池子信息
            pairAddress: pool.pairAddress,
            token0Address: pool.token0Address,
            token1Address: pool.token1Address,
            // 元数据
            metadata: {
                parseSource: 'meteora-event-parser',
                parseTimestamp: Date.now()
            }
        };
    }
    // ==================== 辅助方法 ====================
    generateMessageKey(message) {
        if (message.txHash && message.instructionIndex) {
            return `${message.txHash}-${message.instructionIndex}`;
        }
        else if (message.txHash) {
            return message.txHash;
        }
        else {
            return `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        }
    }
    calculatePrice(token0Amount, token1Amount) {
        try {
            const amount0 = parseFloat(token0Amount);
            const amount1 = parseFloat(token1Amount);
            if (amount0 === 0)
                return '0';
            return (amount1 / amount0).toFixed(8);
        }
        catch (error) {
            return '0';
        }
    }
    calculateVolumeUSD(trade) {
        // TODO: 集成价格预言机获取USD价格
        // 这里暂时返回占位符
        return '0';
    }
    /**
     * 关闭连接
     */
    async close() {
        if (this.kafkaProducer) {
            await this.kafkaProducer.disconnect();
        }
        if (this.rabbitChannel) {
            await this.rabbitChannel.close();
        }
    }
}
exports.MessageQueueService = MessageQueueService;
