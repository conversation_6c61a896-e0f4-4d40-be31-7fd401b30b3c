"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageBroker = exports.WS_MESSAGE_TOPICS = exports.WS_MESSAGE_TYPES = exports.KAFKA_GROUPS = exports.KAFKA_TOPICS = exports.WS_PUSH_EXCHANGE = exports.NEW_POOL_EXCHANGE = void 0;
const kafkajs_1 = require("kafkajs");
const amqp = __importStar(require("amqplib"));
// RabbitMQ Exchange 常量
exports.NEW_POOL_EXCHANGE = 'new_pool_exchange';
exports.WS_PUSH_EXCHANGE = 'ws_push_exchange'; // WebSocket 推送专用交换机
// Kafka Topic 常量 - 完整版（对应 Go 端的 DataWarehouseService topics）
exports.KAFKA_TOPICS = {
    // === Solana DEX 相关 ===
    // 交易入库
    SOLANA_DEX_TRANSACTION: 'solana_dex_transaction',
    // 新池入库
    SOLANA_DEX_NEW_POOL: 'solana_dex_new_pool',
    // 流动性入库
    SOLANA_DEX_LIQUIDITY: 'solana_dex_liquidity',
    // Pump 启动台
    SOLANA_PUMP_LAUNCHPAD: 'solana_dex_pump_launchpad',
    // === 代币相关 ===
    // Solana SPL 代币变更
    SOLANA_SPL_TOKEN_CHANGE: 'solana_spl_token_change',
    // Solana SPL 代币转账
    SOLANA_SPL_TOKEN_TRANSFER: 'solana_spl_token_transfer',
    // Solana 代币更新
    SOLANA_TOKEN_UPDATE: 'token_update_solana',
    // BSC 代币更新
    BSC_TOKEN_UPDATE: 'token_update_bsc',
    // === 数据库同步相关 ===
    // 代币信息同步
    DB_SYNC_TOKENS: 'db_sync_tokens',
    // Pump SPL 代币同步
    DB_SYNC_PUMP_SPL_TOKENS: 'db_sync_pump_spl_tokens',
    // Solana 池子对同步
    DB_SYNC_SOLANA_POOL_PAIRS: 'db_sync_solana_pool_pairs',
    // === 搜索和更新相关 ===
    // ES 代币信息更新
    UPDATE_TOKEN_INFO_ES: 'update_token_info_es',
    // === EVM 相关 ===
    // EVM 新池入库
    EVM_DEX_NEW_POOL: 'evm_dex_new_pool',
    // EVM 交易入库
    EVM_DEX_TRANSACTION: 'evm_dex_transaction',
    // EVM ERC20 转账
    EVM_ERC20_TRANSFER: 'evm_erc20_transfer',
    // EVM 原生代币转账
    EVM_NATIVE_TRANSFER: 'evm_native_transfer',
    // EVM 代币余额变更
    EVM_TOKEN_BALANCE: 'evm_token_balance_change',
    // EVM Meme 启动台
    EVM_MEME_LAUNCHPAD: 'evm_meme_launchpad_topic',
    // EVM 池子流动性更新
    EVM_POOL_LIQUIDITY_UPDATE: 'evm_pool_liquidity_update',
    // === 用户相关 ===
    // 用户资产快照
    USER_ASSETS_SNAPSHOT: 'user_assets_snapshot',
    // Solana 用户代币收益更新
    SOLANA_USER_TOKEN_PROFIT_UPDATE: 'solana_user_token_profit',
    // EVM 用户代币收益更新
    EVM_USER_TOKEN_PROFIT_UPDATE: 'evm_user_token_profit',
    // === 转账相关 ===
    // 纯转账 (根据环境动态设置前缀)
    PURE_TRANSFERS: 'pure_transfers',
    PURE_TRANSFERS_DEV: 'event-center.pure_transfers',
    // === WebSocket 推送 ===
    WEBSOCKET_PUSH: 'websocket_push',
};
// Kafka Consumer Group 常量
exports.KAFKA_GROUPS = {
    TOKEN_UPDATE_SOLANA: 'token_update_group_solana',
};
// WebSocket 消息类型常量（对应 Go 端的 enums.SocketMessageType）
exports.WS_MESSAGE_TYPES = {
    // 基础连接消息
    CONNECT_SUCCESS: 'ConnectSuccess',
    SUBSCRIBE: 'Subscribe',
    UNSUBSCRIBE: 'Unsubscribe',
    PING: 'Ping',
    PONG: 'Pong',
};
// WebSocket 消息主题常量（对应 Go 端的 enums.SocketMessageTopic）
exports.WS_MESSAGE_TOPICS = {
    // 链信息
    ETH_GAS_PRICE: 'EthGasPrice',
    SOLANA_GAS_PRICE: 'SolanaGasPrice',
    SOLANA_LATEST_BLOCK_HASH: 'SolanaLatestBlockHash',
    NATIVE_COIN_PRICE: 'NativeCoinPrice',
    JITO_TIP_FLOOR: 'JitoTipFloor',
    // Pump.fun 相关
    PUMP_NEW_PAIRS: 'Solana.Pump.NewPairs',
    PUMP_COMPLETED: 'Solana.Pump.Completed',
    PUMP_NOT_COMPLETED: 'Solana.Pump.NotCompleted',
    PUMP_PAIR_TRADE: 'Solana.Pump.RealTimePairTrade.%s', // %s 为池子地址
    PUMP_PAIR_READY_TO_LAUNCH: 'Solana.Pump.PairReadyToLaunch',
    // 交易对相关
    PAIR_MARKET_INFO_UPDATE: 'Solana.Pair.MarketInfoUpdate',
    REAL_TIME_PAIR_TRADE: 'RealTimePairTrade.%s.%s', // %s.%s 为链名.池子地址
    // 用户相关
    NOTIFICATION: 'Notification.%d', // %d 为用户ID
    USER_MESSAGE_UNREAD: 'UserMessage.UnRead',
    USER_MESSAGE_NEW: 'UserMessage.New',
};
class MessageBroker {
    constructor() {
        this.kafkaProducer = null;
        this.amqpConn = null;
        this.amqpChannel = null;
    }
    /**
     * 格式化 WebSocket 消息主题（对应 Go 端的 Format2SocketMessageType）
     * @param topic 主题模板
     * @param args 参数
     * @returns 格式化后的主题
     */
    static formatTopic(topic, ...args) {
        let formattedTopic = topic;
        args.forEach((arg, index) => {
            if (typeof arg === 'string') {
                formattedTopic = formattedTopic.replace('%s', arg);
            }
            else if (typeof arg === 'number') {
                formattedTopic = formattedTopic.replace('%d', arg.toString());
            }
        });
        return formattedTopic;
    }
    /**
     * 根据环境获取正确的 Topic 名称（对应 Go 端的环境判断逻辑）
     * @param baseTopic 基础 Topic 名称
     * @param isDev 是否为开发环境
     * @returns 环境相关的 Topic 名称
     */
    static getEnvironmentTopic(baseTopic, isDev = false) {
        // 对于特定的 topics，在非生产环境添加前缀
        if (isDev && baseTopic === exports.KAFKA_TOPICS.PURE_TRANSFERS) {
            return exports.KAFKA_TOPICS.PURE_TRANSFERS_DEV;
        }
        return baseTopic;
    }
    /**
     * 检查是否为生产环境
     * @returns 是否为生产环境
     */
    static isProd() {
        return process.env.NODE_ENV === 'production';
    }
    static getInstance() {
        if (!MessageBroker.instance) {
            MessageBroker.instance = new MessageBroker();
        }
        return MessageBroker.instance;
    }
    async connect() {
        // Kafka
        const kafkaBrokers = (process.env.KAFKA_BROKERS || 'localhost:9092').split(',');
        if (kafkaBrokers[0]) {
            const kafka = new kafkajs_1.Kafka({
                clientId: 'ts-debot',
                brokers: kafkaBrokers
            });
            this.kafkaProducer = kafka.producer();
            await this.kafkaProducer.connect();
            console.log('Kafka Producer 连接成功');
        }
        // RabbitMQ
        if (process.env.AMQP_URL) {
            this.amqpConn = await amqp.connect(process.env.AMQP_URL);
            this.amqpChannel = await this.amqpConn.createChannel();
            await this.amqpChannel.assertExchange(exports.NEW_POOL_EXCHANGE, 'fanout', { durable: true });
            await this.amqpChannel.assertExchange(exports.WS_PUSH_EXCHANGE, 'fanout', { durable: true });
            console.log('RabbitMQ 连接成功');
        }
    }
    async publish(topic, message) {
        // Kafka
        if (this.kafkaProducer) {
            await this.kafkaProducer.send({
                topic: topic,
                messages: [{ value: JSON.stringify(message) }],
            });
            console.log(`[Kafka] 消息已发送到 topic: ${topic}`);
        }
        else {
            console.warn(`[Kafka] Producer 未连接，无法发送消息到 topic: ${topic}`);
        }
    }
    // WebSocket 专用推送方法（对应 Go 的 WebSocketService.PublishMessage）
    async publishWebSocket(message) {
        // 使用 RabbitMQ 进行 WebSocket 推送（对应 Go 的 PushExchange）
        if (this.amqpChannel) {
            const msgBuffer = Buffer.from(JSON.stringify(message));
            this.amqpChannel.publish(exports.WS_PUSH_EXCHANGE, '', msgBuffer);
            console.log(`[WebSocket] 消息已发送到 RabbitMQ exchange: ${exports.WS_PUSH_EXCHANGE}`);
        }
        else {
            console.warn('[WebSocket] RabbitMQ 未连接，无法发送 WebSocket 消息');
        }
    }
    async close() {
        if (this.kafkaProducer) {
            await this.kafkaProducer.disconnect();
            console.log('Kafka Producer 已断开连接');
        }
        if (this.amqpChannel) {
            await this.amqpChannel.close();
            console.log('RabbitMQ Channel 已关闭');
        }
        if (this.amqpConn) {
            await this.amqpConn.close();
            console.log('RabbitMQ Connection 已关闭');
        }
    }
}
exports.MessageBroker = MessageBroker;
