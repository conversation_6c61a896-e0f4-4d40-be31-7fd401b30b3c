/**
 * Chain and coin related constants and functions
 * TypeScript implementation of Go's internal/common/consts/chain_coin.go
 */

// Chain name enum
export enum ChainName {
    Solana = 'solana',
    Ethereum = 'eth',
    BnbChain = 'bsc',
    Base = 'base'
}

// Native coin fake address for virtual native tokens
export const NATIVE_COIN_FAKE_ADDRESS = '******************************************';

// Native coin addresses
export const SOL_ADDRESS = '********************************';
export const WRAPPED_SOL_ADDRESS = 'So********************************111111112';
export const WRAPPED_BNB_ADDRESS = '******************************************';
export const WRAPPED_ETH_ADDRESS = '******************************************';
export const WRAPPED_BASE_ETH_ADDRESS = '******************************************';
export const OKX_EVM_NATIVE_COIN_ADDRESS = '******************************************';

// Token information constants
export const SOL_TOKEN_NAME = 'SOL';
export const SOL_TOKEN_SYMBOL = 'SOL';
export const SOL_TOKEN_DECIMAL = 9;
export const SOL_LOGO_URL = 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So********************************111111112/logo.png';

export const WRAPPED_SOL_TOKEN_NAME = 'Wrapped SOL';
export const WRAPPED_SOL_TOKEN_SYMBOL = 'WSOL';
export const WRAPPED_SOL_TOKEN_DECIMAL = 9;
export const WRAPPED_SOL_LOGO_URL = 'https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So********************************111111112/logo.png';

export const BNB_TOKEN_NAME = 'BNB';
export const BNB_TOKEN_SYMBOL = 'BNB';
export const BNB_TOKEN_DECIMALS = 18;
export const BNB_LOGO_URL = 'https://raw.githubusercontent.com/bnb-chain/wallet-assets/refs/heads/master/assets/BNB/logo.svg';

export const WRAPPED_BNB_TOKEN_NAME = 'Wrapped BNB';
export const WRAPPED_BNB_TOKEN_SYMBOL = 'WBNB';
export const WRAPPED_BNB_TOKEN_DECIMALS = 18;
export const WRAPPED_BNB_LOGO_URL = 'https://raw.githubusercontent.com/bnb-chain/wallet-assets/refs/heads/master/assets/BNB/logo.svg';

// Chain support mapping
export const CHAIN_MAP: Record<ChainName, boolean> = {
    [ChainName.Solana]: true,
    [ChainName.Ethereum]: true,
    [ChainName.BnbChain]: true,
    [ChainName.Base]: true
};

// Chain native coin decimals mapping
export const CHAIN_NATIVE_COIN_DECIMALS_MAP: Record<ChainName, number> = {
    [ChainName.Solana]: 9,
    [ChainName.Ethereum]: 18,
    [ChainName.BnbChain]: 18,
    [ChainName.Base]: 18
};

// Chain native coin address mapping
export const CHAIN_NATIVE_COIN_MAP: Record<ChainName, string> = {
    [ChainName.Solana]: WRAPPED_SOL_ADDRESS,
    [ChainName.Ethereum]: WRAPPED_ETH_ADDRESS,
    [ChainName.BnbChain]: WRAPPED_BNB_ADDRESS,
    [ChainName.Base]: WRAPPED_BASE_ETH_ADDRESS
};

// Chain native coin symbol mapping
export const CHAIN_NATIVE_COIN_SYMBOL: Record<ChainName, string> = {
    [ChainName.Solana]: SOL_TOKEN_NAME,
    [ChainName.Ethereum]: 'ETH',
    [ChainName.BnbChain]: 'BNB',
    [ChainName.Base]: 'ETH'
};

// Coin info interface
export interface CoinInfo {
    chainName: ChainName;
    address: string;
    name: string;
    symbol: string;
    decimals: number;
    logoURL: string;
}

// Chain native coin info mapping
export const CHAIN_NATIVE_COIN_INFO_MAP: Record<ChainName, CoinInfo> = {
    [ChainName.Solana]: {
        chainName: ChainName.Solana,
        address: WRAPPED_SOL_ADDRESS,
        name: WRAPPED_SOL_TOKEN_NAME,
        symbol: WRAPPED_SOL_TOKEN_SYMBOL,
        decimals: SOL_TOKEN_DECIMAL,
        logoURL: SOL_LOGO_URL
    },
    [ChainName.BnbChain]: {
        chainName: ChainName.BnbChain,
        address: WRAPPED_BNB_ADDRESS,
        name: WRAPPED_BNB_TOKEN_NAME,
        symbol: WRAPPED_BNB_TOKEN_SYMBOL,
        decimals: WRAPPED_BNB_TOKEN_DECIMALS,
        logoURL: WRAPPED_BNB_LOGO_URL
    },
    [ChainName.Ethereum]: {
        chainName: ChainName.Ethereum,
        address: WRAPPED_ETH_ADDRESS,
        name: 'Wrapped ETH',
        symbol: 'WETH',
        decimals: 18,
        logoURL: ''
    },
    [ChainName.Base]: {
        chainName: ChainName.Base,
        address: WRAPPED_BASE_ETH_ADDRESS,
        name: 'Wrapped ETH',
        symbol: 'WETH',
        decimals: 18,
        logoURL: ''
    }
};

/**
 * Check if a chain is supported
 */
export function isChainSupport(chainName: ChainName): boolean {
    return CHAIN_MAP[chainName] === true;
}

/**
 * Get all supported chains
 */
export function getSupportChains(): ChainName[] {
    return Object.keys(CHAIN_MAP).filter(chain => CHAIN_MAP[chain as ChainName]) as ChainName[];
}

/**
 * Get chain native coin info
 */
export function getChainNativeCoinInfo(chainName: ChainName): CoinInfo {
    return CHAIN_NATIVE_COIN_INFO_MAP[chainName];
}

/**
 * Check if an address is a native coin for the given chain
 * Handles case sensitivity properly for different chains
 */
export function isChainNativeCoin(chainName: ChainName, address: string): boolean {
    if (!address) {
        return false;
    }

    if (NATIVE_COIN_FAKE_ADDRESS === address) {
        return true;
    }

    // Check if chain is supported
    const nativeCoinAddress = CHAIN_NATIVE_COIN_MAP[chainName];
    if (!nativeCoinAddress) {
        return false;
    }

    switch (chainName) {
        case ChainName.Solana:
            // SOL addresses are case sensitive
            return address === nativeCoinAddress || address === SOL_ADDRESS;
        default:
            // EVM chains are case insensitive
            return address.toLowerCase() === nativeCoinAddress.toLowerCase() ||
                   address.toLowerCase() === OKX_EVM_NATIVE_COIN_ADDRESS.toLowerCase();
    }
}

/**
 * Check if an address is either a native coin or stable coin for the given chain
 * This is the main function that combines both checks
 */
export function isChainNativeOrStableCoin(chainName: ChainName, address: string): boolean {
    // Import here to avoid circular dependency
    const { isChainStableCoin } = require('./stableCoin');
    return isChainNativeCoin(chainName, address) || isChainStableCoin(chainName, address);
}

/**
 * Get base token address from two token addresses
 * Returns the address that is either native coin or stable coin, prioritizing native coin
 */
export function getBaseTokenAddress(chainName: ChainName, token0Address: string, token1Address: string): string {
    if (isChainNativeCoin(chainName, token0Address)) {
        return token1Address;
    } else if (isChainNativeCoin(chainName, token1Address)) {
        return token0Address;
    } else {
        // Import here to avoid circular dependency
        const { isChainStableCoin } = require('./stableCoin');
        if (isChainStableCoin(chainName, token1Address)) {
            return token0Address;
        } else if (isChainStableCoin(chainName, token0Address)) {
            return token1Address;
        }
    }
    return token0Address;
}
